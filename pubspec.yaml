name: agrimart
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.7.2

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  firebase_core: ^3.13.1 # Check for the latest version
  firebase_auth: ^5.5.4 # Check for the latest version
  cloud_firestore: ^5.6.8 # Check for the latest version
  firebase_storage: ^12.4.6 # Check for the latest version (for later use)

  path_provider: ^2.1.2 # or latest
  path: ^1.9.0 # or latest
  # For state management (optional, but good practice - e.g., provider)
  provider: ^6.1.2
  flutterwave_standard: ^1.0.7
  image_picker: ^1.0.7 # Check for the latest version
  uuid: ^4.3.3 # For generating unique IDs (optional but good)
  flutter_native_splash: ^2.4.6
  webview_flutter: ^4.4.1
  flutter_inappwebview: ^6.0.0
  fluttertoast: ^8.2.4
  intl: ^0.20.2
  
dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0
  mockito: ^5.4.4
  build_runner: ^2.4.9
  flutter_launcher_icons: ^0.13.1

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true
  assets:
    - assets/images/ # This makes all images in this folder availabl

flutter_native_splash:
  # background_image: "assets/images/splash_background.png" # Optional: if you want a full background image
  # --- Colors for simple background (if not using background_image) ---
  color: "#E8F5E9" # A very light, earthy green or off-white
  color_dark: "#1B5E20" # A darker green for dark mode
  image: assets/images/agrimart-splash.png
  fullscreen: true

flutter_launcher_icons:
  android: "launcher_icon"
  ios: true
  image_path: "assets/images/agrimart_icon.png"
  min_sdk_android: 21 # android min sdk min:16, default 21
  web:
    generate: true
    image_path: "assets/images/agrimart_icon.png"
    background_color: "#E8F5E9"
    theme_color: "#4CAF50"
  windows:
    generate: true
    image_path: "assets/images/agrimart_icon.png"
    icon_size: 48 # min:48, max:256, default: 48


