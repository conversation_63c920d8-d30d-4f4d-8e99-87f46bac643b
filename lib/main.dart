// lib/main.dart
import 'package:flutter/material.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:provider/provider.dart';
import 'package:agrimart/firebase_options.dart';
import 'package:agrimart/providers/cart_provider.dart';
import 'package:agrimart/services/auth_service.dart';
import 'package:agrimart/services/firestore_service.dart';
import 'package:agrimart/app.dart'; // Import the App widget

// Enhanced Agricultural Marketplace Color Palette
class AppColors {
  // Primary Green Palette - Fresh & Natural
  static const Color primaryGreen = Color(0xFF2E7D32); // Deep forest green
  static const Color primaryGreenDark = Color(0xFF1B5E20); // Darker forest
  static const Color primaryGreenLight = Color(0xFF4CAF50); // Fresh green
  static const Color primaryGreenAccent = Color(
    0xFF81C784,
  ); // Light accent green
  static const Color primaryGreenSoft = Color(
    0xFFE8F5E8,
  ); // Very light green background

  // Secondary Earth Tones
  static const Color earthBrown = Color(0xFF6D4C41); // Rich soil brown
  static const Color earthTan = Color(0xFFD7CCC8); // Light earth tone
  static const Color earthBeige = Color(0xFFF5F5DC); // Warm beige
  static const Color warmOrange = Color(
    0xFFFF8A65,
  ); // Sunset orange for accents

  // Neutral Palette
  static const Color textPrimary = Color(0xFF1A1A1A); // Rich black
  static const Color textSecondary = Color(0xFF666666); // Medium gray
  static const Color textTertiary = Color(0xFF999999); // Light gray
  static const Color textOnPrimary = Colors.white;
  static const Color textOnDark = Colors.white;

  // Background Colors
  static const Color scaffoldBackground = Color(0xFFFAFAFA); // Clean white-gray
  static const Color cardBackground = Colors.white;
  static const Color surfaceLight = Color(0xFFF8F9FA); // Very light surface
  static const Color divider = Color(0xFFE0E0E0);

  // Status Colors
  static const Color success = Color(0xFF4CAF50);
  static const Color warning = Color(0xFFFF9800);
  static const Color error = Color(0xFFE53935);
  static const Color info = Color(0xFF2196F3);

  // Special Agricultural Colors
  static const Color freshGreen = Color(0xFF8BC34A); // Fresh produce
  static const Color organicGreen = Color(0xFF689F38); // Organic badge
  static const Color premiumGold = Color(0xFFFFB300); // Premium products
}

ThemeData buildLightTheme() {
  final base = ThemeData.light(useMaterial3: true);

  return base.copyWith(
    primaryColor: AppColors.primaryGreen,
    scaffoldBackgroundColor: AppColors.scaffoldBackground,
    // Enhanced Color Scheme
    colorScheme: const ColorScheme.light(
      primary: AppColors.primaryGreen,
      onPrimary: AppColors.textOnPrimary,
      secondary: AppColors.earthBrown,
      onSecondary: AppColors.textOnDark,
      tertiary: AppColors.warmOrange,
      onTertiary: AppColors.textOnDark,
      error: AppColors.error,
      onError: Colors.white,
      surface: AppColors.cardBackground,
      onSurface: AppColors.textPrimary,
      surfaceContainerHighest: AppColors.surfaceLight,
      outline: AppColors.divider,
      outlineVariant: AppColors.primaryGreenSoft,
    ),

    // Enhanced Typography
    textTheme: base.textTheme
        .copyWith(
          headlineSmall: base.textTheme.headlineSmall?.copyWith(
            color: AppColors.textPrimary,
            fontWeight: FontWeight.bold,
            // fontFamily: 'YourHeadlineFont', // If different
          ),
          headlineMedium: base.textTheme.headlineMedium?.copyWith(
            color: AppColors.textPrimary,
            fontWeight: FontWeight.bold,
          ),
          titleLarge: base.textTheme.titleLarge?.copyWith(
            color: AppColors.textPrimary,
            fontWeight: FontWeight.w600,
          ),
          titleMedium: base.textTheme.titleMedium?.copyWith(
            color: AppColors.textPrimary,
          ),
          bodyLarge: base.textTheme.bodyLarge?.copyWith(
            color: AppColors.textPrimary,
            fontSize: 16.0,
          ),
          bodyMedium: base.textTheme.bodyMedium?.copyWith(
            color: AppColors.textSecondary,
            fontSize: 14.0,
          ),
          labelLarge: base.textTheme.labelLarge?.copyWith(
            // For button text
            color: AppColors.textOnPrimary,
            fontWeight: FontWeight.w600,
            fontSize: 16.0,
          ),
        )
        .apply(
          // fontFamily: 'Poppins', // Apply default font to all text styles
          displayColor: AppColors.textPrimary,
          bodyColor: AppColors.textPrimary,
        ),

    // AppBar Theme
    appBarTheme: const AppBarTheme(
      backgroundColor: AppColors.primaryGreen,
      foregroundColor: AppColors.textOnPrimary, // Color of icons and title
      elevation: 2.0, // Subtle shadow
      titleTextStyle: TextStyle(
        // fontFamily: 'Poppins',
        fontSize: 20.0,
        fontWeight: FontWeight.w600,
        color: AppColors.textOnPrimary,
      ),
      iconTheme: IconThemeData(color: AppColors.textOnPrimary),
    ),

    // Button Themes
    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        backgroundColor: AppColors.primaryGreen,
        foregroundColor: AppColors.textOnPrimary,
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
        textStyle: const TextStyle(
          // fontFamily: 'Poppins',
          fontSize: 16,
          fontWeight: FontWeight.w600,
        ),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8.0)),
        elevation: 2.0,
      ),
    ),
    outlinedButtonTheme: OutlinedButtonThemeData(
      style: OutlinedButton.styleFrom(
        foregroundColor: AppColors.primaryGreen,
        side: const BorderSide(color: AppColors.primaryGreen, width: 1.5),
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
        textStyle: const TextStyle(
          // fontFamily: 'Poppins',
          fontSize: 16,
          fontWeight: FontWeight.w600,
        ),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8.0)),
      ),
    ),
    textButtonTheme: TextButtonThemeData(
      style: TextButton.styleFrom(
        foregroundColor: AppColors.primaryGreen,
        textStyle: const TextStyle(
          // fontFamily: 'Poppins',
          fontSize: 16,
          fontWeight: FontWeight.w500,
        ),
      ),
    ),

    // Card Theme
    cardTheme: CardTheme(
      elevation: 2.0, // Gentle shadow
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12.0)),
      margin: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
      color: AppColors.cardBackground,
    ),

    // Input Decoration Theme (for text fields)
    inputDecorationTheme: InputDecorationTheme(
      filled: true,
      fillColor: AppColors.surfaceLight,
      hintStyle: const TextStyle(color: AppColors.textTertiary),
      labelStyle: const TextStyle(color: AppColors.primaryGreen),
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12.0),
        borderSide: BorderSide.none,
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12.0),
        borderSide: const BorderSide(color: AppColors.divider, width: 1.0),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8.0),
        borderSide: const BorderSide(color: AppColors.primaryGreen, width: 2.0),
      ),
      errorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8.0),
        borderSide: const BorderSide(color: Colors.redAccent, width: 1.5),
      ),
      focusedErrorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8.0),
        borderSide: const BorderSide(color: Colors.redAccent, width: 2.0),
      ),
      contentPadding: const EdgeInsets.symmetric(
        horizontal: 16.0,
        vertical: 12.0,
      ),
    ),

    // Floating Action Button Theme
    floatingActionButtonTheme: const FloatingActionButtonThemeData(
      backgroundColor: AppColors.earthBrown,
      foregroundColor: AppColors.textOnDark,
      elevation: 4.0,
    ),

    // Icon Theme
    iconTheme: const IconThemeData(
      color: AppColors.primaryGreen, // Default icon color
    ),

    // Chip Theme
    chipTheme: const ChipThemeData(
      backgroundColor: AppColors.primaryGreenSoft,
      disabledColor: AppColors.divider,
      selectedColor: AppColors.primaryGreen,
      secondarySelectedColor: AppColors.primaryGreen,
      padding: EdgeInsets.symmetric(horizontal: 12.0, vertical: 8.0),
      labelStyle: TextStyle(
        color: AppColors.textSecondary,
        fontWeight: FontWeight.w500,
      ),
      secondaryLabelStyle: TextStyle(
        color: AppColors.textOnPrimary,
        fontWeight: FontWeight.w500,
      ),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.all(Radius.circular(20.0)),
      ),
      brightness: Brightness.light,
    ),

    // Page transitions (example, more on this later)
    pageTransitionsTheme: const PageTransitionsTheme(
      builders: {
        TargetPlatform.android:
            CupertinoPageTransitionsBuilder(), // Use iOS-style transitions
        TargetPlatform.iOS: CupertinoPageTransitionsBuilder(),
      },
    ),
  );
}

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);

  runApp(
    MultiProvider(
      providers: [
        Provider<AuthService>(create: (_) => AuthService()),
        Provider<FirestoreService>(create: (_) => FirestoreService()),
        ChangeNotifierProvider(create: (ctx) => CartProvider()),
      ],
      child: const App(), // Use the App widget directly
    ),
  );
}
