import 'package:firebase_auth/firebase_auth.dart';
import 'package:agrimart/models/user_model.dart'; // Adjust import path
import 'package:agrimart/services/firestore_service.dart'; // Adjust import path

class AuthService {
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirestoreService _firestoreService = FirestoreService();

  // Get current user
  User? get currentUser => _auth.currentUser;

  // Auth state changes stream
  Stream<User?> get authStateChanges => _auth.authStateChanges();

  // Sign up with email and password
  Future<User?> signUpWithEmailPassword(
    String email,
    String password,
    String displayName,
    UserRole role,
  ) async {
    try {
      UserCredential userCredential = await _auth
          .createUserWithEmailAndPassword(email: email, password: password);
      User? user = userCredential.user;
      if (user != null) {
        await user.updateDisplayName(displayName);
        await _firestoreService.setUserData(user, displayName, role);
        // Reload user to get updated displayName
        await user.reload();
        return _auth.currentUser; // Return the user with updated info
      }
      return null;
    } on FirebaseAuthException catch (e) {
      print("FirebaseAuthException during sign up: ${e.message}");
      throw Exception(e.message); // Rethrow for UI handling
    } catch (e) {
      print("Error during sign up: $e");
      throw Exception("An unexpected error occurred during sign up.");
    }
  }

  // Sign in with email and password
  Future<User?> signInWithEmailPassword(String email, String password) async {
    try {
      UserCredential userCredential = await _auth.signInWithEmailAndPassword(
        email: email,
        password: password,
      );
      return userCredential.user;
    } on FirebaseAuthException catch (e) {
      print("FirebaseAuthException during sign in: ${e.message}");
      throw Exception(e.message); // Rethrow for UI handling
    } catch (e) {
      print("Error during sign in: $e");
      throw Exception("An unexpected error occurred during sign in.");
    }
  }

  // Sign out with MIUI-specific handling
  Future<void> signOut() async {
    try {
      print("AuthService: Starting signOut process...");

      // Check current user before logout
      final userBeforeLogout = _auth.currentUser;
      print("AuthService: User before logout: ${userBeforeLogout?.uid}");

      // Add delay for MIUI devices to prevent race conditions
      await Future.delayed(const Duration(milliseconds: 100));

      // Perform the actual sign out
      print("AuthService: Calling FirebaseAuth.signOut()...");
      await _auth.signOut();
      print("AuthService: FirebaseAuth.signOut() completed");

      // Add another delay to ensure MIUI processes the state change
      await Future.delayed(const Duration(milliseconds: 300));

      // Verify logout was successful
      final userAfterLogout = _auth.currentUser;
      print("AuthService: User after logout: ${userAfterLogout?.uid}");

      if (userAfterLogout != null) {
        print(
          "AuthService: WARNING - User still exists after logout, forcing another signOut",
        );
        // Try one more time for MIUI devices
        await _auth.signOut();
        await Future.delayed(const Duration(milliseconds: 200));

        final finalUser = _auth.currentUser;
        print("AuthService: Final user check: ${finalUser?.uid}");

        if (finalUser != null) {
          throw Exception("Failed to sign out completely on this device");
        }
      }

      print("AuthService: Sign out completed successfully");
    } catch (e) {
      print("AuthService: Error during sign out: $e");
      throw Exception("Failed to sign out: $e");
    }
  }
}
