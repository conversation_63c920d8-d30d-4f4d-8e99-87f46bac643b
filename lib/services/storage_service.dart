// lib/services/storage_service.dart
import 'dart:io';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:uuid/uuid.dart';

class StorageService {
  final FirebaseStorage _storage = FirebaseStorage.instance;
  final Uuid _uuid = Uuid();

  Future<String?> uploadProductImage(File imageFile, String vendorId, {String? existingProductId}) async {
    try {
      String productId = existingProductId ?? _uuid.v4(); // Use existing ID if updating, else new
      String fileName = 'product_image_${_uuid.v4()}.jpg'; // Ensures unique name even if same product ID
      Reference ref = _storage.ref().child('product_images').child(vendorId).child(productId).child(fileName);

      UploadTask uploadTask = ref.putFile(imageFile);
      TaskSnapshot snapshot = await uploadTask;
      String downloadUrl = await snapshot.ref.getDownloadURL();
      return downloadUrl;
    } catch (e) {
      print("Error uploading product image: $e");
      return null;
    }
  }

  Future<void> deleteProductImage(String imageUrl) async {
    if (imageUrl.isEmpty) return;
    try {
      Reference photoRef = _storage.refFromURL(imageUrl);
      await photoRef.delete();
      print("Image deleted successfully: $imageUrl");
    } catch (e) {
      // Handle cases where file might not exist or other errors
      if (e is FirebaseException && e.code == 'object-not-found') {
        print("Image not found, skipping deletion: $imageUrl");
      } else {
        print("Error deleting product image: $e");
        // Optionally rethrow or handle more gracefully
      }
    }
  }
}