// lib/services/payment_service.dart
// ignore_for_file: unused_field

import 'package:flutter/material.dart';
import 'package:flutterwave_standard/flutterwave.dart';
import 'package:uuid/uuid.dart';
import 'package:agrimart/config/app_keys.dart'; // Your API keys

class PaymentService {
  final Uuid _uuid = Uuid();

  Future<void> processPayment({
    required BuildContext context,
    required double amount,
    required String email,
    required String phoneNumber,
    required String customerName,
    required Function(ChargeResponse response, String txRef) onSuccessful,
    required Function(String errorMessage, String txRef) onError,
    required String
    ourUniqueTxRef, // Pre-generated unique transaction reference
  }) async {
    final Customer customer = Customer(
      name: customerName,
      phoneNumber:
          phoneNumber, // Ensure this is a valid Tanzanian number for Mobile Money
      email: email,
    );

    final Flutterwave flutterwave = Flutterwave(
      // context: context,
      publicKey: kFlutterwavePublicKey_Test,
      currency: kFlutterwaveCurrency_Test, // TZS
      redirectUrl:
          'https://agrimart.dev/payment-redirect', // Placeholder, not strictly needed for mobile SDK flow
      txRef:
          ourUniqueTxRef, // VERY IMPORTANT: This must be unique per transaction
      amount: amount.toStringAsFixed(
        0,
      ), // Flutterwave expects amount as string, no decimals for TZS usually
      customer: customer,
      paymentOptions:
          "mobilemoneytanzania, card, ussd", // Customize based on what you want to offer
      customization: Customization(
        title: "Agrimart Checkout",
        description: "Payment for agricultural products",
        logo: "https_your_logo_url.png/logo.png", // Optional: Your app logo
      ),
      isTestMode: true, // Crucial for using test keys
    );

    try {
      print(
        "Initiating Flutterwave payment with TxRef: $ourUniqueTxRef for Amount: $amount",
      );
      final ChargeResponse response = await flutterwave.charge(context);

      // Debug: Log the complete response
      print("=== FLUTTERWAVE RESPONSE DEBUG ===");
      print("Success: ${response.success}");
      print("Status: ${response.status}");
      print("Transaction ID: ${response.transactionId}");
      print("TxRef: ${response.txRef}");

      print("================================");

      // Check for success using multiple criteria
      bool isSuccessful =
          response.success == true ||
          response.transactionId != null ||
          (response.status?.toUpperCase().contains('SUCCESSFUL') == true) ||
          (response.status?.toUpperCase().contains('SUCCESS') == true);

      if (isSuccessful && response.transactionId != null) {
        print(
          "Flutterwave Payment Successful: TxID=${response.transactionId}, TxRef=${response.txRef}, Status=${response.status}",
        );
        onSuccessful(
          response,
          response.txRef ?? ourUniqueTxRef,
        ); // Pass back txRef used by Flutterwave
      } else {
        print(
          "Flutterwave Payment Failed/Cancelled: Status=${response.status}, Success=${response.success}, TxID=${response.transactionId}, TxRef=${response.txRef}",
        );
        onError(
          response.status ?? "Payment failed or was cancelled by user.",
          response.txRef ?? ourUniqueTxRef,
        );
      }
    } catch (error, stackTrace) {
      print("Flutterwave Payment Exception: $error");
      print(stackTrace);
      onError(error.toString(), ourUniqueTxRef);
    }
  }
}
