import 'package:agrimart/models/order_model.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:agrimart/models/user_model.dart';
import 'package:agrimart/models/product_model.dart';
import 'package:agrimart/models/vendor_rating_model.dart';
import 'package:agrimart/models/vendor_analytics_model.dart';
import 'package:firebase_auth/firebase_auth.dart' as fb_auth;

class FirestoreService {
  final FirebaseFirestore _db = FirebaseFirestore.instance;

  // ------------------- User Methods -------------------
  Future<void> setUserData(
    fb_auth.User firebaseUser,
    String? displayName,
    UserRole role,
  ) async {
    final userDocRef = _db.collection('users').doc(firebaseUser.uid);

    // For vendors, use displayName as shopName (this can be improved later)
    String shopName = displayName ?? firebaseUser.displayName ?? "Unknown Shop";

    final user = UserModel(
      uid: firebaseUser.uid,
      email: firebaseUser.email!,
      displayName: shopName,
      role: role,
      createdAt: Timestamp.now(),
    );

    try {
      await userDocRef.set(user.toJson());
      print("User data stored successfully for UID: ${firebaseUser.uid}");
    } catch (e) {
      print("Error setting user data: $e");
      throw Exception("Failed to store user data: $e");
    }
  }

  Future<UserModel?> getUserData(String uid) async {
    final docRef = _db.collection('users').doc(uid);
    final docSnap = await docRef.get();

    if (docSnap.exists) {
      return UserModel.fromFirestore(docSnap);
    }
    return null;
  }

  // Ensure user document exists and has correct role
  Future<bool> ensureUserDocumentExists(
    String uid,
    UserRole expectedRole,
  ) async {
    try {
      final userDoc = await _db.collection('users').doc(uid).get();

      if (!userDoc.exists) {
        print("User document missing for UID: $uid. Creating it...");

        // Get current Firebase user info
        final currentUser = fb_auth.FirebaseAuth.instance.currentUser;
        if (currentUser == null) {
          print("No authenticated user found");
          return false;
        }

        // Create user document
        final newUser = UserModel(
          uid: uid,
          email: currentUser.email!,
          displayName: currentUser.displayName ?? "User",
          role: expectedRole,
          createdAt: Timestamp.now(),
        );

        await _db.collection('users').doc(uid).set(newUser.toJson());
        print("User document created successfully");
        return true;
      }

      // Check if role matches
      final userData = userDoc.data()!;
      final userRole = userData['role'] as String;
      final expectedRoleString =
          expectedRole == UserRole.vendor ? 'vendor' : 'customer';

      if (userRole != expectedRoleString) {
        print("Role mismatch: expected $expectedRoleString, got $userRole");
        return false;
      }

      print("User document exists with correct role: $userRole");
      return true;
    } catch (e) {
      print("Error ensuring user document: $e");
      return false;
    }
  }

  // ------------------- Product Methods -------------------
  Future<String> addProduct(ProductModel product) async {
    try {
      DocumentReference docRef = await _db
          .collection('products')
          .add(product.toJson());
      return docRef.id;
    } catch (e) {
      print("Error adding product: $e");
      throw Exception("Failed to add product: $e");
    }
  }

  Future<void> updateProduct(ProductModel product) async {
    if (product.id == null) {
      throw Exception("Product ID cannot be null for update.");
    }
    try {
      await _db.collection('products').doc(product.id).update(product.toJson());
    } catch (e) {
      print("Error updating product: $e");
      throw Exception("Failed to update product: $e");
    }
  }

  Future<void> deleteProduct(String productId) async {
    try {
      await _db.collection('products').doc(productId).delete();
    } catch (e) {
      print("Error deleting product: $e");
      throw Exception("Failed to delete product: $e");
    }
  }

  Stream<List<ProductModel>> getVendorProducts(String vendorId) {
    return _db
        .collection('products')
        .where('vendorId', isEqualTo: vendorId)
        .orderBy('createdAt', descending: true)
        .snapshots()
        .map(
          (snapshot) =>
              snapshot.docs
                  .map((doc) => ProductModel.fromFirestore(doc))
                  .toList(),
        );
  }

  Future<ProductModel?> getProductById(String productId) async {
    try {
      final docSnap = await _db.collection('products').doc(productId).get();
      if (docSnap.exists) {
        return ProductModel.fromFirestore(docSnap);
      }
      return null;
    } catch (e) {
      print("Error fetching product by ID: $e");
      return null;
    }
  }

  Stream<List<ProductModel>> getAllProducts() {
    return _db
        .collection('products')
        .orderBy('createdAt', descending: true)
        .snapshots()
        .map(
          (snapshot) =>
              snapshot.docs
                  .map((doc) => ProductModel.fromFirestore(doc))
                  .toList(),
        );
  }

  // Get products by category
  Stream<List<ProductModel>> getProductsByCategory(String category) {
    if (category.toLowerCase() == 'all') {
      return getAllProducts();
    }
    return _db
        .collection('products')
        .where('category', isEqualTo: category)
        .orderBy('createdAt', descending: true)
        .snapshots()
        .map(
          (snapshot) =>
              snapshot.docs
                  .map((doc) => ProductModel.fromFirestore(doc))
                  .toList(),
        );
  }

  // --- Order Methods ---
  Future<String> createOrder(OrderModel order) async {
    try {
      DocumentReference docRef = await _db
          .collection('orders')
          .add(order.toJson());
      print(
        'Order created with ID: ${docRef.id} and TxRef: ${order.transactionRef}',
      );
      return docRef.id; // Return the Firestore document ID
    } catch (e) {
      print("Error creating order: $e");
      throw Exception("Failed to create order: $e");
    }
  }

  Future<void> updateOrder(OrderModel order) async {
    if (order.id == null)
      throw Exception("Order ID cannot be null for update.");
    order.updatedAt = Timestamp.now(); // Always update the timestamp
    try {
      await _db.collection('orders').doc(order.id).update(order.toJson());
    } catch (e) {
      print("Error updating order: $e");
      throw Exception("Failed to update order: $e");
    }
  }

  Future<void> updateOrderStatusAndDetails(
    String orderId,
    OrderStatus status, {
    String? disputeReason,
    String? disputeComments,
    String? adminResolutionNotes,
  }) async {
    try {
      Map<String, dynamic> dataToUpdate = {
        'orderStatus': orderStatusToString(status),
        'updatedAt': Timestamp.now(),
      };
      if (disputeReason != null) dataToUpdate['disputeReason'] = disputeReason;
      if (disputeComments != null)
        dataToUpdate['disputeComments'] = disputeComments;
      if (adminResolutionNotes != null)
        dataToUpdate['adminResolutionNotes'] = adminResolutionNotes;

      await _db.collection('orders').doc(orderId).update(dataToUpdate);
    } catch (e) {
      print("Error updating order status/details: $e");
      throw Exception("Failed to update order status/details: $e");
    }
  }

  // Optional: Update order status (e.g., after payment verification or status changes)
  Future<void> updateOrderStatus(
    String orderId,
    OrderStatus status, {
    String? paymentGatewayRef,
  }) async {
    try {
      Map<String, dynamic> dataToUpdate = {
        'orderStatus': orderStatusToString(status),
        'updatedAt': Timestamp.now(),
      };
      if (paymentGatewayRef != null) {
        dataToUpdate['paymentGatewayRef'] = paymentGatewayRef;
      }
      await _db.collection('orders').doc(orderId).update(dataToUpdate);
    } catch (e) {
      print("Error updating order status: $e");
      throw Exception("Failed to update order status: $e");
    }
  }

  // Fetch orders for a customer (for order history later)
  Stream<List<OrderModel>> getCustomerOrders(String customerId) {
    return _db
        .collection('orders')
        .where('customerId', isEqualTo: customerId)
        .orderBy('createdAt', descending: true)
        .snapshots()
        .map(
          (snapshot) =>
              snapshot.docs
                  .map((doc) => OrderModel.fromFirestore(doc))
                  .toList(),
        );
  }

  Stream<OrderModel> getOrderStream(String orderId) {
    return _db
        .collection('orders')
        .doc(orderId)
        .snapshots()
        .map((doc) => OrderModel.fromFirestore(doc));
  }

  Future<OrderModel?> getOrderById(String orderId) async {
    try {
      final docSnap = await _db.collection('orders').doc(orderId).get();
      if (docSnap.exists) {
        return OrderModel.fromFirestore(docSnap);
      }
      return null;
    } catch (e) {
      print("Error fetching order by ID: $e");
      return null;
    }
  }

  // --- Dispute Methods ---
  Future<String> createDispute(DisputeModel dispute) async {
    try {
      DocumentReference docRef = await _db
          .collection('disputes')
          .add(dispute.toJson());
      // Also update the order status to 'disputed'
      await updateOrderStatusAndDetails(
        dispute.orderId,
        OrderStatus.disputed,
        disputeComments: dispute.customerComments,
        disputeReason: dispute.reason,
      );
      return docRef.id;
    } catch (e) {
      print("Error creating dispute: $e");
      throw Exception("Failed to create dispute: $e");
    }
  }

  Stream<List<DisputeModel>> getAllDisputes() {
    // For Admin
    return _db
        .collection('disputes')
        .orderBy('createdAt', descending: true)
        .snapshots()
        .map(
          (snapshot) =>
              snapshot.docs
                  .map((doc) => DisputeModel.fromFirestore(doc))
                  .toList(),
        );
  }

  Future<void> updateDisputeStatus(
    String disputeId,
    String status, {
    String? adminNotes,
  }) async {
    Map<String, dynamic> dataToUpdate = {'status': status};
    if (adminNotes != null) dataToUpdate['adminNotes'] = adminNotes;
    if (status.toLowerCase() == "resolved")
      dataToUpdate['resolvedAt'] = Timestamp.now();
    try {
      await _db.collection('disputes').doc(disputeId).update(dataToUpdate);
    } catch (e) {
      print("Error updating dispute: $e");
      throw Exception("Failed to update dispute: $e");
    }
  }

  // Add vendor response to dispute
  Future<void> addVendorResponseToDispute(
    String disputeId,
    String vendorResponse,
  ) async {
    try {
      await _db.collection('disputes').doc(disputeId).update({
        'vendorResponse': vendorResponse,
        'vendorResponseAt': Timestamp.now(),
        'status': 'UnderReview', // Update status when vendor responds
      });
    } catch (e) {
      print("Error adding vendor response: $e");
      throw Exception("Failed to add vendor response: $e");
    }
  }

  // Get dispute details by order ID
  Future<DisputeModel?> getDisputeByOrderId(String orderId) async {
    try {
      QuerySnapshot snapshot =
          await _db
              .collection('disputes')
              .where('orderId', isEqualTo: orderId)
              .limit(1)
              .get();

      if (snapshot.docs.isNotEmpty) {
        return DisputeModel.fromFirestore(
          snapshot.docs.first as DocumentSnapshot<Map<String, dynamic>>,
        );
      }
      return null;
    } catch (e) {
      print("Error getting dispute by order ID: $e");
      return null;
    }
  }

  // --- Vendor Rating Methods ---
  Future<String> addVendorRating(VendorRatingModel rating) async {
    try {
      DocumentReference docRef = await _db
          .collection('vendor_ratings')
          .add(rating.toJson());
      return docRef.id;
    } catch (e) {
      print("Error adding vendor rating: $e");
      throw Exception("Failed to add vendor rating: $e");
    }
  }

  Stream<List<VendorRatingModel>> getVendorRatings(String vendorId) {
    return _db
        .collection('vendor_ratings')
        .where('vendorId', isEqualTo: vendorId)
        .orderBy('createdAt', descending: true)
        .snapshots()
        .map(
          (snapshot) =>
              snapshot.docs
                  .map((doc) => VendorRatingModel.fromFirestore(doc))
                  .toList(),
        );
  }

  // Get customer ratings stream (ratings given by a customer)
  Stream<List<VendorRatingModel>> getCustomerRatings(String customerId) {
    return _db
        .collection('vendor_ratings')
        .where('customerId', isEqualTo: customerId)
        .orderBy('createdAt', descending: true)
        .snapshots()
        .map(
          (snapshot) =>
              snapshot.docs
                  .map((doc) => VendorRatingModel.fromFirestore(doc))
                  .toList(),
        );
  }

  Future<VendorStats> getVendorStats(String vendorId) async {
    try {
      final ratingsSnapshot =
          await _db
              .collection('vendor_ratings')
              .where('vendorId', isEqualTo: vendorId)
              .get();

      if (ratingsSnapshot.docs.isEmpty) {
        return VendorStats.empty(vendorId);
      }

      final ratings =
          ratingsSnapshot.docs
              .map((doc) => VendorRatingModel.fromFirestore(doc))
              .toList();

      double totalRating = 0;
      Map<int, int> distribution = {1: 0, 2: 0, 3: 0, 4: 0, 5: 0};

      for (final rating in ratings) {
        totalRating += rating.rating;
        int starRating = rating.rating.round();
        distribution[starRating] = (distribution[starRating] ?? 0) + 1;
      }

      double averageRating = totalRating / ratings.length;

      return VendorStats(
        vendorId: vendorId,
        averageRating: averageRating,
        totalRatings: ratings.length,
        ratingDistribution: distribution,
      );
    } catch (e) {
      print("Error fetching vendor stats: $e");
      return VendorStats.empty(vendorId);
    }
  }

  Future<bool> hasCustomerRatedVendor(
    String customerId,
    String vendorId,
  ) async {
    try {
      final snapshot =
          await _db
              .collection('vendor_ratings')
              .where('customerId', isEqualTo: customerId)
              .where('vendorId', isEqualTo: vendorId)
              .limit(1)
              .get();

      return snapshot.docs.isNotEmpty;
    } catch (e) {
      print("Error checking if customer rated vendor: $e");
      return false;
    }
  }

  // --- Customer Analytics Methods ---
  Future<int> getCustomerOrderCount(String customerId) async {
    try {
      final snapshot =
          await _db
              .collection('orders')
              .where('customerId', isEqualTo: customerId)
              .get();
      return snapshot.docs.length;
    } catch (e) {
      print("Error getting customer order count: $e");
      return 0;
    }
  }

  Future<int> getCustomerRatingCount(String customerId) async {
    try {
      final snapshot =
          await _db
              .collection('vendor_ratings')
              .where('customerId', isEqualTo: customerId)
              .get();
      return snapshot.docs.length;
    } catch (e) {
      print("Error getting customer rating count: $e");
      return 0;
    }
  }

  // --- Vendor Analytics Methods ---
  Future<int> getVendorProductCount(String vendorId) async {
    try {
      final snapshot =
          await _db
              .collection('products')
              .where('vendorId', isEqualTo: vendorId)
              .get();
      return snapshot.docs.length;
    } catch (e) {
      print("Error getting vendor product count: $e");
      return 0;
    }
  }

  // Get orders that contain products from a specific vendor
  // Note: This method requires reading all orders and filtering client-side
  // For better performance, consider denormalizing data or using Cloud Functions
  Stream<List<OrderModel>> getVendorOrders(String vendorId) {
    return _db
        .collection('orders')
        .orderBy('createdAt', descending: true)
        .snapshots()
        .map(
          (snapshot) =>
              snapshot.docs
                  .map((doc) => OrderModel.fromFirestore(doc))
                  .where(
                    (order) =>
                        order.items.any((item) => item.vendorId == vendorId),
                  )
                  .toList(),
        );
  }

  // Alternative method to get vendor orders by querying all orders and filtering
  Stream<List<OrderModel>> getVendorOrdersAlternative(String vendorId) {
    return _db
        .collection('orders')
        .orderBy('createdAt', descending: true)
        .snapshots()
        .map(
          (snapshot) =>
              snapshot.docs
                  .map((doc) => OrderModel.fromFirestore(doc))
                  .where(
                    (order) =>
                        order.items.any((item) => item.vendorId == vendorId),
                  )
                  .toList(),
        );
  }

  // Get vendor sales analytics
  Future<VendorSalesAnalytics> getVendorSalesAnalytics(String vendorId) async {
    try {
      // Use optimized query that only fetches orders containing vendor's products
      // Then filter by status client-side (more efficient than reading all orders)
      final ordersSnapshot =
          await _db
              .collection('orders')
              .where('vendorIds', arrayContains: vendorId)
              .get();

      final vendorOrders =
          ordersSnapshot.docs
              .map((doc) => OrderModel.fromFirestore(doc))
              .where(
                (order) =>
                    order.orderStatus == OrderStatus.delivered ||
                    order.orderStatus == OrderStatus.completed,
              )
              .toList();

      double totalRevenue = 0;
      int totalOrders = vendorOrders.length;
      Map<String, int> productSales = {};
      Map<String, double> monthlySales = {};

      for (final order in vendorOrders) {
        final vendorItems = order.items.where(
          (item) => item.vendorId == vendorId,
        );
        for (final item in vendorItems) {
          totalRevenue += item.priceAtPurchase * item.quantity;
          productSales[item.productName] =
              (productSales[item.productName] ?? 0) + item.quantity;
        }

        // Monthly sales
        final monthKey =
            '${order.createdAt.toDate().year}-${order.createdAt.toDate().month.toString().padLeft(2, '0')}';
        final orderTotal = vendorItems.fold<double>(
          0,
          (total, item) => total + (item.priceAtPurchase * item.quantity),
        );
        monthlySales[monthKey] = (monthlySales[monthKey] ?? 0) + orderTotal;
      }

      return VendorSalesAnalytics(
        vendorId: vendorId,
        totalRevenue: totalRevenue,
        totalOrders: totalOrders,
        productSales: productSales,
        monthlySales: monthlySales,
      );
    } catch (e) {
      print("Error getting vendor sales analytics: $e");
      return VendorSalesAnalytics.empty(vendorId);
    }
  }

  // Get disputes involving vendor's products
  Stream<List<DisputeModel>> getVendorDisputes(String vendorId) {
    return _db
        .collection('disputes')
        .where('vendorIds', arrayContains: vendorId)
        .orderBy('createdAt', descending: true)
        .snapshots()
        .map(
          (snapshot) =>
              snapshot.docs
                  .map((doc) => DisputeModel.fromFirestore(doc))
                  .toList(),
        );
  }

  // Optimized vendor orders query using vendorIds array
  Stream<List<OrderModel>> getVendorOrdersOptimized(String vendorId) {
    return _db
        .collection('orders')
        .where('vendorIds', arrayContains: vendorId)
        .orderBy('createdAt', descending: true)
        .snapshots()
        .map(
          (snapshot) =>
              snapshot.docs
                  .map((doc) => OrderModel.fromFirestore(doc))
                  .toList(),
        );
  }

  // Get comprehensive vendor dashboard stats
  Future<VendorDashboardStats> getVendorDashboardStats(String vendorId) async {
    try {
      // Get all data in parallel for better performance
      final futures = await Future.wait([
        getVendorProductCount(vendorId),
        getVendorStats(vendorId),
        getVendorSalesAnalytics(vendorId),
        _getVendorDisputeCount(vendorId),
      ]);

      final productCount = futures[0] as int;
      final vendorStats = futures[1] as VendorStats;
      final salesAnalytics = futures[2] as VendorSalesAnalytics;
      final disputeCount = futures[3] as int;

      return VendorDashboardStats(
        totalProducts: productCount,
        totalOrders: salesAnalytics.totalOrders,
        totalRevenue: salesAnalytics.totalRevenue,
        averageRating: vendorStats.averageRating,
        totalReviews: vendorStats.totalRatings,
        pendingDisputes: disputeCount,
      );
    } catch (e) {
      print("Error getting vendor dashboard stats: $e");
      return VendorDashboardStats.empty();
    }
  }

  // Helper method to get pending dispute count for vendor
  Future<int> _getVendorDisputeCount(String vendorId) async {
    try {
      final snapshot =
          await _db
              .collection('disputes')
              .where('vendorIds', arrayContains: vendorId)
              .where('status', isEqualTo: 'open')
              .get();
      return snapshot.docs.length;
    } catch (e) {
      print("Error getting vendor dispute count: $e");
      return 0;
    }
  }

  // Migration method to add vendorIds to existing orders
  // Call this once to update existing orders
  Future<void> migrateOrdersToIncludeVendorIds() async {
    try {
      final ordersSnapshot = await _db.collection('orders').get();

      for (final doc in ordersSnapshot.docs) {
        final data = doc.data();

        // Skip if vendorIds already exists
        if (data.containsKey('vendorIds')) continue;

        // Extract vendor IDs from items
        final items = data['items'] as List<dynamic>;
        final vendorIds =
            items.map((item) => item['vendorId'] as String).toSet().toList();

        // Update the document with new fields
        await doc.reference.update({
          'vendorIds': vendorIds,
          'transactionStatus': 'held', // Default transaction status
          'vendorShipmentConfirmed': false,
          'customerDeliveryConfirmed': false,
        });
        print("Updated order ${doc.id} with vendorIds: $vendorIds");
      }

      print("Migration completed successfully");
    } catch (e) {
      print("Error during migration: $e");
    }
  }

  // --- Enhanced Confirmation Workflow Methods ---

  // Vendor confirms shipment
  Future<void> confirmVendorShipment(String orderId, String vendorId) async {
    try {
      final orderDoc = await _db.collection('orders').doc(orderId).get();
      if (!orderDoc.exists) {
        throw Exception("Order not found");
      }

      final order = OrderModel.fromFirestore(orderDoc);

      // Verify vendor is involved in this order
      if (!order.vendorIds.contains(vendorId)) {
        throw Exception("Vendor not authorized for this order");
      }

      // Update order with vendor confirmation
      await _db.collection('orders').doc(orderId).update({
        'vendorShipmentConfirmed': true,
        'vendorConfirmedAt': Timestamp.now(),
        'orderStatus': orderStatusToString(OrderStatus.shipped),
        'updatedAt': Timestamp.now(),
      });

      // Check if both confirmations are complete
      await _checkAndCompleteTransaction(orderId);

      print("Vendor $vendorId confirmed shipment for order $orderId");
    } catch (e) {
      print("Error confirming vendor shipment: $e");
      throw Exception("Failed to confirm shipment: $e");
    }
  }

  // Customer confirms delivery
  Future<void> confirmCustomerDelivery(
    String orderId,
    String customerId,
  ) async {
    try {
      final orderDoc = await _db.collection('orders').doc(orderId).get();
      if (!orderDoc.exists) {
        throw Exception("Order not found");
      }

      final order = OrderModel.fromFirestore(orderDoc);

      // Verify customer owns this order
      if (order.customerId != customerId) {
        throw Exception("Customer not authorized for this order");
      }

      // Update order with customer confirmation
      await _db.collection('orders').doc(orderId).update({
        'customerDeliveryConfirmed': true,
        'customerConfirmedAt': Timestamp.now(),
        'orderStatus': orderStatusToString(OrderStatus.delivered),
        'updatedAt': Timestamp.now(),
      });

      // Check if both confirmations are complete
      await _checkAndCompleteTransaction(orderId);

      print("Customer $customerId confirmed delivery for order $orderId");
    } catch (e) {
      print("Error confirming customer delivery: $e");
      throw Exception("Failed to confirm delivery: $e");
    }
  }

  // Frontend helper: Get transaction status based on order status
  // This handles existing orders that don't have transactionStatus field
  static TransactionStatus getTransactionStatusFromOrderStatus(
    OrderStatus orderStatus,
    TransactionStatus? dbTransactionStatus,
  ) {
    // If we have a transaction status in DB, use it (for new orders)
    if (dbTransactionStatus != null) {
      return dbTransactionStatus;
    }

    // For existing orders without transactionStatus, derive from orderStatus
    switch (orderStatus) {
      case OrderStatus.completed:
        return TransactionStatus.released;
      case OrderStatus.disputed:
        return TransactionStatus.disputed;
      case OrderStatus.cancelledByCustomer:
      case OrderStatus.cancelledByVendor:
        return TransactionStatus.refunded;
      default:
        return TransactionStatus
            .held; // All other statuses keep money in escrow
    }
  }

  // Check if both confirmations are complete and release transaction
  Future<void> _checkAndCompleteTransaction(String orderId) async {
    try {
      final orderDoc = await _db.collection('orders').doc(orderId).get();
      if (!orderDoc.exists) {
        print("Order $orderId not found for transaction completion");
        return;
      }

      final order = OrderModel.fromFirestore(orderDoc);

      print("Checking transaction completion for order $orderId:");
      print("  Vendor shipped: ${order.vendorShipmentConfirmed}");
      print("  Customer confirmed: ${order.customerDeliveryConfirmed}");
      print("  Current status: ${orderStatusToString(order.orderStatus)}");

      // For new orders with confirmation fields, use the dual confirmation logic
      if (order.vendorShipmentConfirmed && order.customerDeliveryConfirmed) {
        await _db.collection('orders').doc(orderId).update({
          'orderStatus': orderStatusToString(OrderStatus.completed),
          'transactionStatus': transactionStatusToString(
            TransactionStatus.released,
          ),
          'updatedAt': Timestamp.now(),
        });
        print("✅ Transaction completed and released for order $orderId");
      }
      // For existing orders, complete transaction when status reaches 'completed'
      else if (order.orderStatus == OrderStatus.completed) {
        await _db.collection('orders').doc(orderId).update({
          'transactionStatus': transactionStatusToString(
            TransactionStatus.released,
          ),
          'updatedAt': Timestamp.now(),
        });
        print("✅ Transaction released for completed order $orderId");
      }
    } catch (e) {
      print("Error completing transaction: $e");
    }
  }

  // Test user permissions for dispute creation
  Future<bool> testUserPermissions() async {
    try {
      final currentUser = fb_auth.FirebaseAuth.instance.currentUser;
      if (currentUser == null) {
        print("No authenticated user");
        return false;
      }

      // Try to read user document
      final userDoc = await _db.collection('users').doc(currentUser.uid).get();
      if (!userDoc.exists) {
        print("User document does not exist");
        return false;
      }

      final userData = userDoc.data()!;
      print("User data: $userData");

      // Try to create a test document in disputes collection
      await _db.collection('disputes').add({
        'test': true,
        'customerId': currentUser.uid,
        'createdAt': Timestamp.now(),
      });

      print("Test dispute creation successful");
      return true;
    } catch (e) {
      print("Test failed: $e");
      return false;
    }
  }

  // Handle dispute creation with transaction holding
  Future<String> createDisputeWithHold(DisputeModel dispute) async {
    try {
      // Ensure user document exists and has customer role
      final currentUser = fb_auth.FirebaseAuth.instance.currentUser;
      if (currentUser == null) {
        throw Exception("No authenticated user found");
      }

      print("Verifying user document for dispute creation...");
      final userDocExists = await ensureUserDocumentExists(
        currentUser.uid,
        UserRole.customer,
      );

      if (!userDocExists) {
        throw Exception(
          "User document verification failed. Cannot create dispute.",
        );
      }

      // Create the dispute
      print("Creating dispute with data: ${dispute.toJson()}");
      DocumentReference docRef = await _db
          .collection('disputes')
          .add(dispute.toJson());

      // Update order status and hold transaction
      await _db.collection('orders').doc(dispute.orderId).update({
        'orderStatus': orderStatusToString(OrderStatus.disputed),
        'transactionStatus': transactionStatusToString(
          TransactionStatus.disputed,
        ),
        'disputeReason': dispute.reason,
        'disputeComments': dispute.customerComments,
        'updatedAt': Timestamp.now(),
      });

      print(
        "Dispute created and transaction held for order ${dispute.orderId}",
      );
      return docRef.id;
    } catch (e) {
      print("Error creating dispute with hold: $e");
      // Print more detailed error information
      if (e is FirebaseException) {
        print("Firebase error code: ${e.code}");
        print("Firebase error message: ${e.message}");
      }
      throw Exception("Failed to create dispute: $e");
    }
  }
}
