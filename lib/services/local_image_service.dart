// lib/services/local_image_service.dart
import 'dart:io';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as p; // Alias for path package
import 'package:uuid/uuid.dart';

class LocalImageService {
  final Uuid _uuid = Uuid();

  Future<String?> saveImageLocally(File imageFile, String vendorId) async {
    try {
      final Directory appDocDir = await getApplicationDocumentsDirectory();
      // Create a subdirectory for Agrimart images if it doesn't exist
      final String agrimartImageDirPath = p.join(appDocDir.path, 'agrimart_product_images', vendorId);
      final Directory agrimartImageDir = Directory(agrimartImageDirPath);
      if (!await agrimartImageDir.exists()) {
        await agrimartImageDir.create(recursive: true);
      }

      String fileExtension = p.extension(imageFile.path);
      String newFileName = '${_uuid.v4()}$fileExtension';
      String newPath = p.join(agrimartImageDir.path, newFileName);

      File newImage = await imageFile.copy(newPath);
      print('Image saved locally at: ${newImage.path}');
      return newImage.path; // Store the full local path
    } catch (e) {
      print("Error saving image locally: $e");
      return null;
    }
  }

  Future<void> deleteImageLocally(String? imagePath) async {
    if (imagePath == null || imagePath.isEmpty) return;
    try {
      final File imageFile = File(imagePath);
      if (await imageFile.exists()) {
        await imageFile.delete();
        print("Local image deleted: $imagePath");
      } else {
        print("Local image not found, skipping deletion: $imagePath");
      }
    } catch (e) {
      print("Error deleting local image: $e");
    }
  }
}