import 'package:cloud_firestore/cloud_firestore.dart';

class VendorRatingModel {
  final String id;
  final String vendorId;
  final String customerId;
  final String customerName;
  final double rating; // 1-5 stars
  final String? review;
  final Timestamp createdAt;
  final String? orderId; // Optional: link to specific order

  VendorRatingModel({
    required this.id,
    required this.vendorId,
    required this.customerId,
    required this.customerName,
    required this.rating,
    this.review,
    required this.createdAt,
    this.orderId,
  });

  factory VendorRatingModel.fromFirestore(DocumentSnapshot<Map<String, dynamic>> doc) {
    final data = doc.data()!;
    return VendorRatingModel(
      id: doc.id,
      vendorId: data['vendorId'] as String,
      customerId: data['customerId'] as String,
      customerName: data['customerName'] as String,
      rating: (data['rating'] as num).toDouble(),
      review: data['review'] as String?,
      createdAt: data['createdAt'] as Timestamp,
      orderId: data['orderId'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'vendorId': vendorId,
      'customerId': customerId,
      'customerName': customerName,
      'rating': rating,
      'review': review,
      'createdAt': createdAt,
      'orderId': orderId,
    };
  }
}

class VendorStats {
  final String vendorId;
  final double averageRating;
  final int totalRatings;
  final Map<int, int> ratingDistribution; // star -> count

  VendorStats({
    required this.vendorId,
    required this.averageRating,
    required this.totalRatings,
    required this.ratingDistribution,
  });

  factory VendorStats.empty(String vendorId) {
    return VendorStats(
      vendorId: vendorId,
      averageRating: 0.0,
      totalRatings: 0,
      ratingDistribution: {1: 0, 2: 0, 3: 0, 4: 0, 5: 0},
    );
  }
}
