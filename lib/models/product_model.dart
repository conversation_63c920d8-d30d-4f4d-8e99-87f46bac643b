// lib/models/product_model.dart
import 'package:cloud_firestore/cloud_firestore.dart';

class ProductModel {
  final String? id; // Nullable for new products before saving
  final String vendorId;
  final String vendorShopName; // Denormalized for easier display
  final String title;
  final String description;
  final double price;
  final String category;
  final String imageUrl;
  final int quantity; // Added quantity
  final Timestamp createdAt;
  final Timestamp updatedAt;

  ProductModel({
    this.id,
    required this.vendorId,
    required this.vendorShopName,
    required this.title,
    required this.description,
    required this.price,
    required this.category,
    required this.imageUrl,
    required this.quantity,
    required this.createdAt,
    required this.updatedAt,
  });

  factory ProductModel.fromFirestore(DocumentSnapshot<Map<String, dynamic>> doc) {
    final data = doc.data()!;
    return ProductModel(
      id: doc.id,
      vendorId: data['vendorId'] as String,
      vendorShopName: data['vendorShopName'] as String? ?? 'Unknown Shop',
      title: data['title'] as String,
      description: data['description'] as String,
      price: (data['price'] as num).toDouble(),
      category: data['category'] as String,
      imageUrl: data['imageUrl'] as String,
      quantity: data['quantity'] as int? ?? 1, // Default quantity if not present
      createdAt: data['createdAt'] as Timestamp,
      updatedAt: data['updatedAt'] as Timestamp,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'vendorId': vendorId,
      'vendorShopName': vendorShopName,
      'title': title,
      'description': description,
      'price': price,
      'category': category,
      'imageUrl': imageUrl,
      'quantity': quantity,
      'createdAt': createdAt,
      'updatedAt': updatedAt,
    };
  }
}