class VendorSalesAnalytics {
  final String vendorId;
  final double totalRevenue;
  final int totalOrders;
  final Map<String, int> productSales; // product name -> quantity sold
  final Map<String, double> monthlySales; // month -> revenue

  VendorSalesAnalytics({
    required this.vendorId,
    required this.totalRevenue,
    required this.totalOrders,
    required this.productSales,
    required this.monthlySales,
  });

  factory VendorSalesAnalytics.empty(String vendorId) {
    return VendorSalesAnalytics(
      vendorId: vendorId,
      totalRevenue: 0.0,
      totalOrders: 0,
      productSales: {},
      monthlySales: {},
    );
  }

  double get averageOrderValue =>
      totalOrders > 0 ? totalRevenue / totalOrders : 0.0;

  List<MapEntry<String, int>> get topSellingProducts {
    final entries = productSales.entries.toList();
    entries.sort((a, b) => b.value.compareTo(a.value));
    return entries.take(5).toList();
  }

  List<MapEntry<String, double>> get monthlySalesData {
    final entries = monthlySales.entries.toList();
    entries.sort((a, b) => a.key.compareTo(b.key));
    return entries;
  }
}

class VendorDashboardStats {
  final int totalProducts;
  final int totalOrders;
  final double totalRevenue;
  final double averageRating;
  final int totalReviews;
  final int pendingDisputes;

  VendorDashboardStats({
    required this.totalProducts,
    required this.totalOrders,
    required this.totalRevenue,
    required this.averageRating,
    required this.totalReviews,
    required this.pendingDisputes,
  });

  factory VendorDashboardStats.empty() {
    return VendorDashboardStats(
      totalProducts: 0,
      totalOrders: 0,
      totalRevenue: 0.0,
      averageRating: 0.0,
      totalReviews: 0,
      pendingDisputes: 0,
    );
  }
}
