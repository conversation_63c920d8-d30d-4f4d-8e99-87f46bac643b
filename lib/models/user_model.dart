import 'package:cloud_firestore/cloud_firestore.dart';

enum UserRole { customer, vendor }

class UserModel {
  final String uid;
  final String email;
  final String? displayName;
  final UserRole role;
  final Timestamp createdAt;

  UserModel({
    required this.uid,
    required this.email,
    this.displayName,
    required this.role,
    required this.createdAt,
  });

  // Factory constructor to create a UserModel from a Firestore document
  factory UserModel.fromFirestore(DocumentSnapshot<Map<String, dynamic>> doc) {
    final data = doc.data()!;
    return UserModel(
      uid: doc.id,
      email: data['email'] as String,
      displayName: data['displayName'] as String?,
      role: (data['role'] as String) == 'vendor' ? UserRole.vendor : UserRole.customer,
      createdAt: data['createdAt'] as Timestamp,
    );
  }

  // Method to convert UserModel to a map for Firestore
  Map<String, dynamic> toJson() {
    return {
      'uid': uid,
      'email': email,
      'displayName': displayName,
      'role': role == UserRole.vendor ? 'vendor' : 'customer',
      'createdAt': createdAt,
    };
  }
}