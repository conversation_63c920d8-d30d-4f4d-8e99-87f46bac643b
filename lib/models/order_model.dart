// lib/models/order_model.dart
import 'package:cloud_firestore/cloud_firestore.dart';

enum OrderStatus {
  awaitingPayment, // Before payment (not used in this flow if payment is upfront)
  paymentPending, // Payment initiated but not yet confirmed by gateway (if applicable)
  paymentFailed,
  pendingConfirmation, // Payment successful, order placed, awaiting vendor shipment confirmation
  shipped, // Vendor marks as shipped, awaiting customer delivery confirmation
  disputed, // Customer raised a dispute - TRANSACTION HELD
  delivered, // Customer confirmed delivery - READY FOR RELEASE
  completed, // Both confirmations done - TRANSACTION RELEASED TO VENDOR
  cancelledByCustomer,
  cancelledByVendor,
  resolvedDispute, // Dispute has been resolved by an admin
}

enum TransactionStatus {
  held, // Payment held in escrow
  released, // Payment released to vendor
  refunded, // Payment refunded to customer
  disputed, // Payment held due to dispute
}

// Helper to convert enum to string and vice-versa
String orderStatusToString(OrderStatus status) =>
    status.toString().split('.').last;
OrderStatus orderStatusFromString(String statusStr) {
  return OrderStatus.values.firstWhere(
    (e) =>
        e.toString().split('.').last.toLowerCase() == statusStr.toLowerCase(),
    orElse: () => OrderStatus.pendingConfirmation,
  ); // Default
}

String transactionStatusToString(TransactionStatus status) =>
    status.toString().split('.').last;
TransactionStatus transactionStatusFromString(String statusStr) {
  return TransactionStatus.values.firstWhere(
    (e) =>
        e.toString().split('.').last.toLowerCase() == statusStr.toLowerCase(),
    orElse: () => TransactionStatus.held,
  ); // Default
}

class OrderItemModel {
  final String productId;
  final String productName;
  final int quantity;
  final double priceAtPurchase; // Price per unit at the time of purchase
  final String imageUrl; // Store one image for quick display
  final String vendorId;

  OrderItemModel({
    required this.productId,
    required this.productName,
    required this.quantity,
    required this.priceAtPurchase,
    required this.imageUrl,
    required this.vendorId,
  });

  Map<String, dynamic> toJson() {
    return {
      'productId': productId,
      'productName': productName,
      'quantity': quantity,
      'priceAtPurchase': priceAtPurchase,
      'imageUrl': imageUrl,
      'vendorId': vendorId,
    };
  }

  factory OrderItemModel.fromMap(Map<String, dynamic> map) {
    return OrderItemModel(
      productId: map['productId'] as String,
      productName: map['productName'] as String,
      quantity: map['quantity'] as int,
      priceAtPurchase: (map['priceAtPurchase'] as num).toDouble(),
      imageUrl: map['imageUrl'] as String,
      vendorId: map['vendorId'] as String,
    );
  }
}

class OrderModel {
  final String? id;
  final String customerId;
  final String customerName;
  final String customerEmail;
  final List<OrderItemModel> items;
  final List<String> vendorIds; // Denormalized for efficient queries
  final double totalAmount;
  OrderStatus orderStatus; // Mutable for updates
  TransactionStatus transactionStatus; // Mutable - tracks payment status
  final String paymentGateway;
  final String transactionRef;
  final String? paymentGatewayRef;
  final Timestamp createdAt;
  Timestamp updatedAt; // Mutable
  final String shippingAddress;
  String? disputeReason; // For disputed orders
  String? disputeComments; // Customer comments
  String? adminResolutionNotes; // Admin notes

  // Confirmation tracking
  bool vendorShipmentConfirmed; // Vendor confirms they shipped
  bool customerDeliveryConfirmed; // Customer confirms they received
  Timestamp? vendorConfirmedAt; // When vendor confirmed shipment
  Timestamp? customerConfirmedAt; // When customer confirmed delivery

  OrderModel({
    this.id,
    required this.customerId,
    required this.customerName,
    required this.customerEmail,
    required this.items,
    List<String>? vendorIds,
    required this.totalAmount,
    this.orderStatus = OrderStatus.pendingConfirmation, // Default after payment
    this.transactionStatus =
        TransactionStatus.held, // Default - payment held in escrow
    required this.paymentGateway,
    required this.transactionRef,
    this.paymentGatewayRef,
    required this.createdAt,
    required this.updatedAt,
    required this.shippingAddress,
    this.disputeReason,
    this.disputeComments,
    this.adminResolutionNotes,
    this.vendorShipmentConfirmed = false,
    this.customerDeliveryConfirmed = false,
    this.vendorConfirmedAt,
    this.customerConfirmedAt,
  }) : vendorIds =
           vendorIds ?? items.map((item) => item.vendorId).toSet().toList();

  Map<String, dynamic> toJson() {
    return {
      'customerId': customerId,
      'customerName': customerName,
      'customerEmail': customerEmail,
      'items': items.map((item) => item.toJson()).toList(),
      'vendorIds': vendorIds,
      'totalAmount': totalAmount,
      'orderStatus': orderStatusToString(orderStatus),
      'transactionStatus': transactionStatusToString(transactionStatus),
      'paymentGateway': paymentGateway,
      'transactionRef': transactionRef,
      'paymentGatewayRef': paymentGatewayRef,
      'createdAt': createdAt,
      'updatedAt': updatedAt,
      'shippingAddress': shippingAddress,
      'disputeReason': disputeReason,
      'disputeComments': disputeComments,
      'adminResolutionNotes': adminResolutionNotes,
      'vendorShipmentConfirmed': vendorShipmentConfirmed,
      'customerDeliveryConfirmed': customerDeliveryConfirmed,
      'vendorConfirmedAt': vendorConfirmedAt,
      'customerConfirmedAt': customerConfirmedAt,
    };
  }

  factory OrderModel.fromFirestore(DocumentSnapshot<Map<String, dynamic>> doc) {
    final data = doc.data()!;
    final items =
        (data['items'] as List<dynamic>)
            .map(
              (itemData) =>
                  OrderItemModel.fromMap(itemData as Map<String, dynamic>),
            )
            .toList();

    return OrderModel(
      id: doc.id,
      customerId: data['customerId'] as String,
      customerName: data['customerName'] as String,
      customerEmail: data['customerEmail'] as String,
      items: items,
      vendorIds:
          data['vendorIds'] != null
              ? List<String>.from(data['vendorIds'] as List)
              : items
                  .map((item) => item.vendorId)
                  .toSet()
                  .toList(), // Fallback for old orders
      totalAmount: (data['totalAmount'] as num).toDouble(),
      orderStatus: orderStatusFromString(
        data['orderStatus'] as String? ?? 'pendingConfirmation',
      ),
      transactionStatus: transactionStatusFromString(
        data['transactionStatus'] as String? ?? 'held',
      ),
      paymentGateway: data['paymentGateway'] as String,
      transactionRef: data['transactionRef'] as String,
      paymentGatewayRef: data['paymentGatewayRef'] as String?,
      createdAt: data['createdAt'] as Timestamp,
      updatedAt:
          data['updatedAt'] as Timestamp? ??
          data['createdAt'] as Timestamp, // Fallback for older docs
      shippingAddress: data['shippingAddress'] as String? ?? "N/A",
      disputeReason: data['disputeReason'] as String?,
      disputeComments: data['disputeComments'] as String?,
      adminResolutionNotes: data['adminResolutionNotes'] as String?,
      vendorShipmentConfirmed:
          data['vendorShipmentConfirmed'] as bool? ?? false,
      customerDeliveryConfirmed:
          data['customerDeliveryConfirmed'] as bool? ?? false,
      vendorConfirmedAt: data['vendorConfirmedAt'] as Timestamp?,
      customerConfirmedAt: data['customerConfirmedAt'] as Timestamp?,
    );
  }
}

// --- Dispute Model (Separate collection or embedded, trying separate for now) ---
class DisputeModel {
  final String? id; // Firestore document ID
  final String orderId;
  final String customerId;
  final List<String> vendorIds; // List of vendors involved in the order
  final String reason;
  final String customerComments;
  final Timestamp createdAt;
  Timestamp? resolvedAt;
  String? adminNotes;
  String status; // "Open", "UnderReview", "Resolved"
  String? vendorResponse; // Vendor's response to the dispute
  Timestamp? vendorResponseAt; // When vendor responded

  DisputeModel({
    this.id,
    required this.orderId,
    required this.customerId,
    required this.vendorIds,
    required this.reason,
    required this.customerComments,
    required this.createdAt,
    this.resolvedAt,
    this.adminNotes,
    this.status = "Open",
    this.vendorResponse,
    this.vendorResponseAt,
  });

  Map<String, dynamic> toJson() => {
    'orderId': orderId,
    'customerId': customerId,
    'vendorIds': vendorIds,
    'reason': reason,
    'customerComments': customerComments,
    'createdAt': createdAt,
    'resolvedAt': resolvedAt,
    'adminNotes': adminNotes,
    'status': status,
    'vendorResponse': vendorResponse,
    'vendorResponseAt': vendorResponseAt,
  };

  factory DisputeModel.fromFirestore(
    DocumentSnapshot<Map<String, dynamic>> doc,
  ) {
    final data = doc.data()!;
    return DisputeModel(
      id: doc.id,
      orderId: data['orderId'] as String,
      customerId: data['customerId'] as String,
      vendorIds: List<String>.from(data['vendorIds'] as List? ?? []),
      reason: data['reason'] as String,
      customerComments: data['customerComments'] as String,
      createdAt: data['createdAt'] as Timestamp,
      resolvedAt: data['resolvedAt'] as Timestamp?,
      adminNotes: data['adminNotes'] as String?,
      status: data['status'] as String? ?? 'Open',
      vendorResponse: data['vendorResponse'] as String?,
      vendorResponseAt: data['vendorResponseAt'] as Timestamp?,
    );
  }
}
