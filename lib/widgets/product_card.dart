import 'package:flutter/material.dart';
import 'dart:io';
import '../models/product_model.dart';
import '../main.dart';

class ProductCard extends StatelessWidget {
  final ProductModel product;
  final VoidCallback? onTap;
  final VoidCallback? onAddToCart;
  final bool showAddToCart;

  const ProductCard({
    super.key,
    required this.product,
    this.onTap,
    this.onAddToCart,
    this.showAddToCart = true,
  });

  Widget _buildProductImage() {
    File imageFile = File(product.imageUrl);

    if (product.imageUrl.isNotEmpty && imageFile.existsSync()) {
      return ClipRRect(
        borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
        child: Image.file(
          imageFile,
          height: 160,
          width: double.infinity,
          fit: BoxFit.cover,
          errorBuilder:
              (context, error, stackTrace) => _buildPlaceholderImage(),
        ),
      );
    } else {
      return _buildPlaceholderImage();
    }
  }

  Widget _buildPlaceholderImage() {
    return Container(
      height: 160,
      width: double.infinity,
      decoration: const BoxDecoration(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [AppColors.primaryGreenSoft, AppColors.primaryGreenAccent],
        ),
      ),
      child: const Icon(Icons.eco, size: 48, color: AppColors.primaryGreen),
    );
  }

  Widget _buildPriceTag() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: AppColors.primaryGreen,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        'TZS ${product.price.toStringAsFixed(0)}',
        style: const TextStyle(
          color: AppColors.textOnPrimary,
          fontWeight: FontWeight.bold,
          fontSize: 12,
        ),
      ),
    );
  }

  Widget _buildQuantityBadge() {
    final isLowStock = product.quantity < 10;
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: isLowStock ? AppColors.warning : AppColors.success,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Text(
        '${product.quantity} left',
        style: const TextStyle(
          color: Colors.white,
          fontSize: 10,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  Widget _buildCategoryChip() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: AppColors.primaryGreenSoft,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.primaryGreenAccent, width: 1),
      ),
      child: Text(
        product.category,
        style: const TextStyle(
          color: AppColors.primaryGreen,
          fontSize: 10,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      shadowColor: AppColors.primaryGreen.withValues(alpha: 0.1),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Image Section with Overlays
            Stack(
              children: [
                _buildProductImage(),
                // Price Tag (Top Right)
                Positioned(top: 8, right: 8, child: _buildPriceTag()),
                // Quantity Badge (Top Left)
                Positioned(top: 8, left: 8, child: _buildQuantityBadge()),
                // Category Chip (Bottom Right of Image)
                Positioned(bottom: 8, right: 8, child: _buildCategoryChip()),
              ],
            ),

            // Content Section
            Flexible(
              child: ClipRect(
                child: Padding(
                  padding: const EdgeInsets.all(2),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // Product Title
                      Text(
                        product.title,
                        style: const TextStyle(
                          fontSize: 11,
                          fontWeight: FontWeight.w600,
                          color: AppColors.textPrimary,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),

                      // Vendor Shop Name
                      Text(
                        'by ${product.vendorShopName}',
                        style: const TextStyle(
                          fontSize: 8,
                          color: AppColors.textSecondary,
                          fontStyle: FontStyle.italic,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),

                      // Description Preview
                      Flexible(
                        child: Text(
                          product.description,
                          style: const TextStyle(
                            fontSize: 8,
                            color: AppColors.textTertiary,
                            height: 1.0,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),

                      // Add to Cart Button - Only show if there's space
                      if (showAddToCart) ...[
                        SizedBox(
                          width: double.infinity,
                          height: 11,
                          child: ElevatedButton(
                            onPressed:
                                product.quantity > 0 ? onAddToCart : null,
                            style: ElevatedButton.styleFrom(
                              backgroundColor:
                                  product.quantity > 0
                                      ? AppColors.primaryGreen
                                      : AppColors.textTertiary,
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(
                                horizontal: 2,
                              ),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(3),
                              ),
                              minimumSize: const Size(0, 16),
                            ),
                            child: Text(
                              product.quantity > 0 ? '+' : '✗',
                              style: const TextStyle(
                                fontSize: 10,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
