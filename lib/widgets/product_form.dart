// lib/widgets/product_form.dart
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';

class ProductForm extends StatefulWidget {
  final GlobalKey<FormState> formKey;
  final TextEditingController titleController;
  final TextEditingController descriptionController;
  final TextEditingController priceController;
  final TextEditingController categoryController;
  final TextEditingController quantityController;
  final Function(File? image) onImagePicked;
  final String? initialImageUrl; // For edit mode

  const ProductForm({
    super.key,
    required this.formKey,
    required this.titleController,
    required this.descriptionController,
    required this.priceController,
    required this.categoryController,
    required this.quantityController,
    required this.onImagePicked,
    this.initialImageUrl,
  });

  @override
  State<ProductForm> createState() => _ProductFormState();
}

class _ProductFormState extends State<ProductForm> {
  File? _pickedImageFile;
  final ImagePicker _picker = ImagePicker();

  Future<void> _pickImage(ImageSource source) async {
    try {
      final pickedFile = await _picker.pickImage(
        source: source,
        imageQuality: 70,
        maxWidth: 800,
      );
      if (pickedFile != null) {
        setState(() {
          _pickedImageFile = File(pickedFile.path);
        });
        widget.onImagePicked(_pickedImageFile);
      }
    } catch (e) {
      print("Error picking image: $e");
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text("Error picking image: $e")));
    }
  }

  void _showImageSourceActionSheet(BuildContext context) {
    showModalBottomSheet(
      context: context,
      builder: (BuildContext bc) {
        return SafeArea(
          child: Wrap(
            children: <Widget>[
              ListTile(
                leading: const Icon(Icons.photo_library),
                title: const Text('Photo Library'),
                onTap: () {
                  _pickImage(ImageSource.gallery);
                  Navigator.of(context).pop();
                },
              ),
              ListTile(
                leading: const Icon(Icons.photo_camera),
                title: const Text('Camera'),
                onTap: () {
                  _pickImage(ImageSource.camera);
                  Navigator.of(context).pop();
                },
              ),
            ],
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    Widget imageWidget;
    if (_pickedImageFile != null) {
      imageWidget = Image.file(_pickedImageFile!, fit: BoxFit.cover);
    } else if (widget.initialImageUrl != null &&
        widget.initialImageUrl!.isNotEmpty) {
      // Check if it's a local path or a network URL (though for this setup, it's always local)
      File localFile = File(widget.initialImageUrl!);
      if (localFile.existsSync()) {
        // Check if file exists before trying to display
        imageWidget = Image.file(localFile, fit: BoxFit.cover);
      } else {
        // Fallback if path is stored but file is missing (e.g. after app reinstall or manual deletion)
        imageWidget = const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.broken_image, size: 40, color: Colors.grey),
              Text("Image not found", style: TextStyle(color: Colors.grey)),
            ],
          ),
        );
      }
    } else {
      imageWidget = const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.camera_alt, size: 40, color: Colors.grey),
            Text("Tap to select image"),
          ],
        ),
      );
    }

    return Form(
      key: widget.formKey,
      child: ListView(
        padding: const EdgeInsets.all(16.0),
        children: [
          GestureDetector(
            onTap: () => _showImageSourceActionSheet(context),
            child: Container(
              height: 150,
              width: double.infinity,
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey),
              ),
              child: imageWidget,
            ),
          ),
          const SizedBox(height: 16),
          TextFormField(
            controller: widget.titleController,
            decoration: const InputDecoration(
              labelText: 'Product Title',
              border: OutlineInputBorder(),
            ),
            validator:
                (value) => value!.isEmpty ? 'Please enter a title' : null,
          ),
          const SizedBox(height: 12),
          TextFormField(
            controller: widget.descriptionController,
            decoration: const InputDecoration(
              labelText: 'Description',
              border: OutlineInputBorder(),
            ),
            maxLines: 3,
            validator:
                (value) => value!.isEmpty ? 'Please enter a description' : null,
          ),
          const SizedBox(height: 12),
          TextFormField(
            controller: widget.priceController,
            decoration: const InputDecoration(
              labelText: 'Price (TZS)',
              border: OutlineInputBorder(),
            ),
            keyboardType: const TextInputType.numberWithOptions(decimal: true),
            validator: (value) {
              if (value!.isEmpty) return 'Please enter a price';
              if (double.tryParse(value) == null || double.parse(value) <= 0)
                return 'Please enter a valid price';
              return null;
            },
          ),
          const SizedBox(height: 12),
          TextFormField(
            controller: widget.quantityController,
            decoration: const InputDecoration(
              labelText: 'Quantity Available',
              border: OutlineInputBorder(),
            ),
            keyboardType: TextInputType.number,
            validator: (value) {
              if (value!.isEmpty) return 'Please enter quantity';
              if (int.tryParse(value) == null || int.parse(value) < 0)
                return 'Please enter a valid quantity';
              return null;
            },
          ),
          const SizedBox(height: 12),
          DropdownButtonFormField<String>(
            value:
                widget.categoryController.text.isEmpty
                    ? null
                    : widget.categoryController.text,
            decoration: const InputDecoration(
              labelText: 'Category',
              border: OutlineInputBorder(),
            ),
            items: const [
              DropdownMenuItem(value: 'Honey', child: Text('Honey')),
              DropdownMenuItem(value: 'Maize', child: Text('Maize')),
              DropdownMenuItem(value: 'Sorghum', child: Text('Sorghum')),
            ],
            onChanged: (String? newValue) {
              if (newValue != null) {
                widget.categoryController.text = newValue;
              }
            },
            validator:
                (value) =>
                    value == null || value.isEmpty
                        ? 'Please select a category'
                        : null,
          ),
          const SizedBox(height: 20),
        ],
      ),
    );
  }
}
