import 'package:flutter/material.dart';
import '../main.dart';
import '../models/user_model.dart';

class UserProfileAvatar extends StatelessWidget {
  final UserModel user;
  final double size;
  final VoidCallback? onTap;
  final bool showOnlineIndicator;

  const UserProfileAvatar({
    super.key,
    required this.user,
    this.size = 40,
    this.onTap,
    this.showOnlineIndicator = false,
  });

  String _getInitials(String? displayName, String email) {
    if (displayName != null && displayName.isNotEmpty) {
      List<String> nameParts = displayName.trim().split(' ');
      if (nameParts.length >= 2) {
        return '${nameParts[0][0]}${nameParts[1][0]}'.toUpperCase();
      } else if (nameParts.isNotEmpty) {
        return nameParts[0].length >= 2 
            ? nameParts[0].substring(0, 2).toUpperCase()
            : nameParts[0][0].toUpperCase();
      }
    }
    
    // Fallback to email initials
    if (email.isNotEmpty) {
      String emailPrefix = email.split('@')[0];
      return emailPrefix.length >= 2 
          ? emailPrefix.substring(0, 2).toUpperCase()
          : emailPrefix[0].toUpperCase();
    }
    
    return 'U'; // Ultimate fallback
  }

  Color _getAvatarColor(String initials) {
    // Generate a consistent color based on initials
    int hash = initials.hashCode;
    List<Color> colors = [
      AppColors.primaryGreen,
      AppColors.primaryGreenLight,
      AppColors.earthBrown,
      AppColors.warmOrange,
      AppColors.organicGreen,
      AppColors.freshGreen,
    ];
    return colors[hash.abs() % colors.length];
  }

  @override
  Widget build(BuildContext context) {
    final initials = _getInitials(user.displayName, user.email);
    final avatarColor = _getAvatarColor(initials);

    return GestureDetector(
      onTap: onTap,
      child: Stack(
        children: [
          Container(
            width: size,
            height: size,
            decoration: BoxDecoration(
              color: avatarColor,
              shape: BoxShape.circle,
              border: Border.all(
                color: AppColors.cardBackground,
                width: 2,
              ),
              boxShadow: [
                BoxShadow(
                  color: avatarColor.withOpacity(0.3),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Center(
              child: Text(
                initials,
                style: TextStyle(
                  color: AppColors.textOnPrimary,
                  fontSize: size * 0.4,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
          if (showOnlineIndicator)
            Positioned(
              right: 0,
              bottom: 0,
              child: Container(
                width: size * 0.25,
                height: size * 0.25,
                decoration: BoxDecoration(
                  color: AppColors.success,
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: AppColors.cardBackground,
                    width: 2,
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }
}

class ProfileHeader extends StatelessWidget {
  final UserModel user;
  final VoidCallback? onEditPressed;

  const ProfileHeader({
    super.key,
    required this.user,
    this.onEditPressed,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(24),
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppColors.primaryGreen,
            AppColors.primaryGreenLight,
          ],
        ),
      ),
      child: SafeArea(
        child: Column(
          children: [
            UserProfileAvatar(
              user: user,
              size: 80,
              showOnlineIndicator: true,
            ),
            const SizedBox(height: 16),
            Text(
              user.displayName ?? 'User',
              style: const TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: AppColors.textOnPrimary,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              user.email,
              style: const TextStyle(
                fontSize: 14,
                color: AppColors.textOnPrimary,
              ),
            ),
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: AppColors.textOnPrimary.withOpacity(0.2),
                borderRadius: BorderRadius.circular(16),
              ),
              child: Text(
                user.role == UserRole.vendor ? 'Vendor' : 'Customer',
                style: const TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                  color: AppColors.textOnPrimary,
                ),
              ),
            ),
            if (onEditPressed != null) ...[
              const SizedBox(height: 16),
              ElevatedButton.icon(
                onPressed: onEditPressed,
                icon: const Icon(Icons.edit, size: 18),
                label: const Text('Edit Profile'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.textOnPrimary,
                  foregroundColor: AppColors.primaryGreen,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(20),
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}
