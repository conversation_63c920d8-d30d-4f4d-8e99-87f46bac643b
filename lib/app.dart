// lib/app.dart
import 'package:flutter/material.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:agrimart/screens/auth/login_screen.dart';
import 'package:agrimart/screens/home_screen.dart'; // This is your dispatcher
import 'package:agrimart/screens/splash_screen.dart';
import 'package:agrimart/main.dart'; // Import for theme

// Global navigator key for navigation control from anywhere in the app
final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

class App extends StatefulWidget {
  const App({super.key});

  @override
  State<App> createState() => _AppState();
}

class _AppState extends State<App> {
  bool _showSplash = true;

  @override
  void initState() {
    super.initState();
    // Show splash screen for 3 seconds
    Future.delayed(const Duration(milliseconds: 3000), () {
      if (mounted) {
        setState(() {
          _showSplash = false;
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    print("App.dart build: MaterialApp is being built.");

    return MaterialApp(
      title: 'Agrimart',
      theme: buildLightTheme(), // Use the custom theme from main.dart
      themeMode: ThemeMode.light,
      // Use the global navigator key for navigation control
      navigatorKey: navigatorKey,
      home:
          _showSplash
              ? const SplashScreen()
              : StreamBuilder<User?>(
                stream: FirebaseAuth.instance.authStateChanges(),
                builder: (context, snapshot) {
                  final currentTime = DateTime.now().millisecondsSinceEpoch;
                  print(
                    "App.dart StreamBuilder: Builder called at $currentTime. ConnectionState: ${snapshot.connectionState}, HasData: ${snapshot.hasData}, User: ${snapshot.data?.uid}",
                  );
                  print(
                    "App.dart StreamBuilder: Raw snapshot data: ${snapshot.data}",
                  );
                  print(
                    "App.dart StreamBuilder: Snapshot error: ${snapshot.error}",
                  );
                  print(
                    "App.dart StreamBuilder: Current Firebase user: ${FirebaseAuth.instance.currentUser?.uid}",
                  );

                  if (snapshot.connectionState == ConnectionState.waiting) {
                    print(
                      "App.dart StreamBuilder: STATE_WAITING - Showing loading",
                    );
                    return const Scaffold(
                      body: Center(child: CircularProgressIndicator()),
                    );
                  }

                  if (snapshot.hasError) {
                    print(
                      "App.dart StreamBuilder: STATE_ERROR - ${snapshot.error}",
                    );
                    return Scaffold(
                      body: Center(
                        child: Text("Auth Stream Error: ${snapshot.error}"),
                      ),
                    );
                  }

                  if (snapshot.hasData && snapshot.data != null) {
                    print(
                      "App.dart StreamBuilder: STATE_LOGGED_IN (UID: ${snapshot.data!.uid}). Returning HomeScreen.",
                    );
                    // By providing a new key each time the user changes, we ensure HomeScreen is rebuilt.
                    return HomeScreen(key: ValueKey(snapshot.data!.uid));
                  } else {
                    print(
                      "App.dart StreamBuilder: STATE_LOGGED_OUT (data is null). Returning LoginScreen.",
                    );
                    print(
                      "App.dart StreamBuilder: About to return LoginScreen widget",
                    );
                    return const LoginScreen(key: ValueKey('loginScreen'));
                  }
                },
              ),
      debugShowCheckedModeBanner: false,
    );
  }
}
