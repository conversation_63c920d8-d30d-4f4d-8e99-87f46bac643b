// lib/screens/splash_screen.dart
import 'package:flutter/material.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();

    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1500), // Fade in duration
    );

    _fadeAnimation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeIn,
    );

    _animationController.forward(); // Start the fade-in animation
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Use theme colors for consistency if already available,
    // or define fallback colors that match your native splash
    final backgroundColor =
        Theme.of(context).brightness == Brightness.dark
            ? const Color(0xFF1B5E20) // Dark green
            : const Color(
              0xFFF0F4F0,
            ); // Very light, almost white with a hint of green

    final primaryColor = Theme.of(context).primaryColor; // From your main theme

    return Scaffold(
      backgroundColor: backgroundColor,
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: <Widget>[
              // Agrimart Logo - ensure this asset exists and is styled well
              Image.asset(
                'assets/images/agrimart-splash.png', // Use your best splash logo
                width: MediaQuery.of(context).size.width * 0.55,
              ),
              const SizedBox(height: 60),
              SizedBox(
                width: MediaQuery.of(context).size.width * 0.6,
                child: LinearProgressIndicator(
                  backgroundColor: primaryColor.withOpacity(0.2),
                  valueColor: AlwaysStoppedAnimation<Color>(primaryColor),
                  minHeight: 6, // Thicker progress bar
                ),
              ),
              const SizedBox(height: 25),
              Text(
                "Freshness Delivered.",
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w500,
                  color: primaryColor.withOpacity(0.9),
                  // fontFamily: 'Poppins', // Use your app's font
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
