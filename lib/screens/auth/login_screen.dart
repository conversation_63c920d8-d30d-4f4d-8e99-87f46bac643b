import 'package:flutter/material.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:provider/provider.dart';
import 'package:agrimart/services/auth_service.dart'; // Adjust import path
import 'package:agrimart/screens/auth/signup_screen.dart'; // Adjust import path
import 'package:agrimart/screens/home_screen.dart';

class LoginScreen extends StatefulWidget {
  const LoginScreen({super.key});

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  final AuthService _authService = AuthService();
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  bool _isLoading = false;
  String? _errorMessage;

  Future<void> _login() async {
    if (_formKey.currentState!.validate()) {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });
      try {
        print(
          "LoginScreen: Starting login for ${_emailController.text.trim()}",
        );
        final user = await _authService.signInWithEmailPassword(
          _emailController.text.trim(),
          _passwordController.text.trim(),
        );
        print("LoginScreen: Login successful for user: ${user?.uid}");

        // Add a small delay to ensure Firebase auth state propagates
        await Future.delayed(const Duration(milliseconds: 500));

        // Force check the current auth state
        final currentUser = FirebaseAuth.instance.currentUser;
        print(
          "LoginScreen: Current Firebase user after delay: ${currentUser?.uid}",
        );

        // Try to manually trigger a rebuild by accessing the auth service
        if (mounted) {
          final authService = Provider.of<AuthService>(context, listen: false);
          final authUser = authService.currentUser;
          print("LoginScreen: AuthService current user: ${authUser?.uid}");
        }

        print(
          "LoginScreen: Navigation to HomeScreen will be handled by StreamBuilder in App.dart",
        );

        // Wait a bit more and try to force navigation if StreamBuilder doesn't work
        await Future.delayed(const Duration(milliseconds: 1000));

        if (mounted) {
          final stillCurrentUser = FirebaseAuth.instance.currentUser;
          print(
            "LoginScreen: Still logged in after 1 second: ${stillCurrentUser?.uid}",
          );

          // If we're still on the login screen after 1 second, something is wrong
          // Let's try manual navigation as a fallback
          if (stillCurrentUser != null) {
            print(
              "LoginScreen: StreamBuilder didn't navigate, trying manual navigation",
            );
            // Manual navigation as fallback
            Navigator.pushReplacement(
              context,
              MaterialPageRoute(
                builder:
                    (context) =>
                        HomeScreen(key: ValueKey(stillCurrentUser.uid)),
              ),
            );
          }
        }

        // Navigation to HomeScreen will be handled by StreamBuilder in App.dart
      } catch (e) {
        print("LoginScreen: Login failed with error: $e");
        setState(() {
          _errorMessage = e.toString().replaceFirst("Exception: ", "");
        });
      } finally {
        if (mounted) {
          // Check if the widget is still in the tree
          setState(() {
            _isLoading = false;
          });
        }
      }
    }
  }

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text("Agrimart Login")),
      body: Center(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(20.0),
          child: Form(
            key: _formKey,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: <Widget>[
                const Text(
                  "Welcome to Agrimart",
                  style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 30),
                TextFormField(
                  controller: _emailController,
                  decoration: const InputDecoration(
                    labelText: "Email",
                    border: OutlineInputBorder(),
                  ),
                  keyboardType: TextInputType.emailAddress,
                  validator: (value) {
                    if (value == null ||
                        value.isEmpty ||
                        !value.contains('@')) {
                      return 'Please enter a valid email';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 20),
                TextFormField(
                  controller: _passwordController,
                  decoration: const InputDecoration(
                    labelText: "Password",
                    border: OutlineInputBorder(),
                  ),
                  obscureText: true,
                  validator: (value) {
                    if (value == null || value.isEmpty || value.length < 6) {
                      return 'Password must be at least 6 characters';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 30),
                if (_isLoading)
                  const CircularProgressIndicator()
                else
                  ElevatedButton(
                    onPressed: _login,
                    style: ElevatedButton.styleFrom(
                      minimumSize: const Size(double.infinity, 50),
                      textStyle: const TextStyle(fontSize: 18),
                    ),
                    child: const Text("Login"),
                  ),
                if (_errorMessage != null)
                  Padding(
                    padding: const EdgeInsets.only(top: 15.0),
                    child: Text(
                      _errorMessage!,
                      style: const TextStyle(color: Colors.red),
                    ),
                  ),
                const SizedBox(height: 20),
                TextButton(
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const SignUpScreen(),
                      ),
                    );
                  },
                  child: const Text("Don't have an account? Sign Up"),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
