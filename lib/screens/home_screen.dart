// lib/screens/home_screen.dart
import 'package:flutter/material.dart';
import 'package:agrimart/services/auth_service.dart';
import 'package:agrimart/services/firestore_service.dart';
import 'package:agrimart/models/user_model.dart' as AppUser; // Alias is good
import 'package:firebase_auth/firebase_auth.dart' as fb_auth;
import 'package:agrimart/screens/vendor/enhanced_vendor_dashboard_screen.dart';
import 'package:agrimart/screens/customer/customer_home_screen.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  final AuthService _authService = AuthService();
  final FirestoreService _firestoreService = FirestoreService();
  AppUser.UserModel? _currentUserData;
  bool _isLoading = true; // Start as true
  String? _lastLoadedUserId; // Track which user we loaded data for

  @override
  void initState() {
    super.initState();
    print("HomeScreen initState: Called. Starting to load user data.");
    _loadUserData();
  }

  @override
  void didUpdateWidget(HomeScreen oldWidget) {
    super.didUpdateWidget(oldWidget);
    // Check if the user changed (different key means different user)
    if (widget.key != oldWidget.key) {
      print("HomeScreen didUpdateWidget: User changed, reloading data");
      _currentUserData = null;
      _isLoading = true;
      _lastLoadedUserId = null;
      _loadUserData();
    }
  }

  Future<void> _loadUserData() async {
    fb_auth.User? firebaseUser = _authService.currentUser;

    // Check if we already loaded data for this user
    if (firebaseUser != null &&
        _lastLoadedUserId == firebaseUser.uid &&
        _currentUserData != null) {
      print(
        "HomeScreen _loadUserData: Data already loaded for user ${firebaseUser.uid}, skipping reload",
      );
      return;
    }

    // Ensure isLoading is true when we start loading, even if called again.
    if (!_isLoading) {
      // If somehow _loadUserData is called when not loading, reset state.
      if (mounted) {
        setState(() {
          _isLoading = true;
          _currentUserData = null; // Reset user data if reloading
        });
      }
    }

    if (firebaseUser != null) {
      print(
        "HomeScreen _loadUserData: Found Firebase user: ${firebaseUser.uid} (Email: ${firebaseUser.email})",
      );
      try {
        _currentUserData = await _firestoreService.getUserData(
          firebaseUser.uid,
        );
        if (_currentUserData != null) {
          _lastLoadedUserId = firebaseUser.uid; // Track which user we loaded
          print(
            "HomeScreen _loadUserData: Successfully fetched UserModel. Role: ${_currentUserData!.role}, Name: ${_currentUserData!.displayName}, Email: ${_currentUserData!.email}",
          );
        } else {
          // This is a critical point if it happens
          print(
            "HomeScreen _loadUserData: FirestoreService.getUserData returned NULL for UID: ${firebaseUser.uid}. User document might be missing or unparsable.",
          );
        }
      } catch (e) {
        print(
          "HomeScreen _loadUserData: ERROR fetching UserModel for UID ${firebaseUser.uid}: $e",
        );
      }
    } else {
      print(
        "HomeScreen _loadUserData: No current Firebase user found (_authService.currentUser is null). This is unexpected if navigated here after login.",
      );
    }

    if (mounted) {
      // Check if the widget is still in the widget tree
      print(
        "HomeScreen _loadUserData: Setting state with isLoading = false. _currentUserData is ${_currentUserData == null ? 'null' : 'NOT null'}",
      );
      setState(() {
        _isLoading = false;
      });
    } else {
      print(
        "HomeScreen _loadUserData: Attempted to setState but widget was unmounted.",
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    print(
      "HomeScreen build: Called. isLoading = $_isLoading, _currentUserData is ${_currentUserData == null ? 'null' : 'NOT null'}",
    );

    // Use theme colors
    final Color primaryLoadingColor = Theme.of(context).primaryColor;
    final Color secondaryTextColor =
        Theme.of(context).textTheme.bodyMedium?.color ?? Colors.grey;
    final Color backgroundColor = Theme.of(context).scaffoldBackgroundColor;

    // Condition 1: Still loading data
    if (_isLoading) {
      print("HomeScreen build: STATE_LOADING_DATA");
      return Scaffold(
        backgroundColor: backgroundColor,
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                "Loading user data...",
                style: TextStyle(fontSize: 16, color: secondaryTextColor),
              ),
              const SizedBox(height: 20),
              CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(primaryLoadingColor),
              ),
            ],
          ),
        ),
      );
    }

    // Condition 2: Loading finished, and user data IS available
    if (!_isLoading && _currentUserData != null) {
      print(
        "HomeScreen build: STATE_USER_DATA_LOADED. Role: ${_currentUserData!.role}. Attempting navigation.",
      );
      // Use WidgetsBinding to schedule navigation after the current frame.
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          // Double check mounted before navigation
          if (_currentUserData!.role == AppUser.UserRole.vendor) {
            print(
              "HomeScreen build (post-frame): Navigating to VendorDashboardScreen.",
            );
            Navigator.pushReplacement(
              context,
              MaterialPageRoute(
                builder:
                    (context) => EnhancedVendorDashboardScreen(
                      currentUserData: _currentUserData!,
                    ),
              ),
            );
          } else if (_currentUserData!.role == AppUser.UserRole.customer) {
            print(
              "HomeScreen build (post-frame): Navigating to CustomerHomeScreen.",
            );
            try {
              final result = Navigator.pushReplacement(
                context,
                MaterialPageRoute(
                  builder:
                      (context) => CustomerHomeScreen(
                        currentUserData: _currentUserData!,
                      ),
                ),
              );
              print(
                "HomeScreen: Navigation to CustomerHomeScreen initiated successfully",
              );
              result
                  .then((_) {
                    print(
                      "HomeScreen: Navigation to CustomerHomeScreen completed",
                    );
                  })
                  .catchError((error) {
                    print(
                      "HomeScreen: Navigation to CustomerHomeScreen failed: $error",
                    );
                  });
            } catch (e) {
              print(
                "HomeScreen: Error during navigation to CustomerHomeScreen: $e",
              );
            }
          } else {
            // This case should ideally not be reached if roles are strictly 'vendor' or 'customer'
            print(
              "HomeScreen build (post-frame): ERROR - Unknown user role: ${_currentUserData!.role}. Staying on error screen.",
            );
            // To prevent continuous loop if stuck, we might need a different UI here instead of just the loading indicator.
            // For now, the fallback UI below will handle this if navigation doesn't occur.
            // But if this else is hit, it's a data problem.
          }
        } else {
          print(
            "HomeScreen build (post-frame): Widget unmounted before navigation could occur.",
          );
        }
      });
      // Return a placeholder UI *while* the navigation is being scheduled by addPostFrameCallback.
      // This is what the user sees for a fraction of a second.
      return Scaffold(
        // Apply themed Scaffold
        backgroundColor: backgroundColor,
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(primaryLoadingColor),
              ),
              const SizedBox(height: 20),
              Text(
                "Preparing your experience...", // More engaging text
                style: TextStyle(fontSize: 16, color: secondaryTextColor),
              ),
            ],
          ),
        ),
      );
    }

    // Condition 3: Loading finished, but user data is NOT available (or some other error)
    // This is the fallback if _isLoading is false AND _currentUserData is null.
    print(
      "HomeScreen build: STATE_LOADING_FINISHED_NO_USER_DATA. Displaying error/logout UI.",
    );
    return Scaffold(
      backgroundColor: backgroundColor,
      appBar: AppBar(
        title: const Text("Agrimart - Error"),
        actions: [
          IconButton(
            icon: const Icon(Icons.logout),
            onPressed: () async {
              print("HomeScreen Error UI: Sign out button pressed.");
              await _authService.signOut();
              print(
                "HomeScreen Error UI: signOut completed. StreamBuilder should handle navigation.",
              );
              // The StreamBuilder in App.dart will automatically handle navigation to LoginScreen
              // when it detects the auth state change (user becomes null)
            },
          ),
        ],
      ),
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: <Widget>[
              const Icon(Icons.error_outline, color: Colors.red, size: 50),
              const SizedBox(height: 20),
              const Text(
                "Could not load your profile.",
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 10),
              const Text(
                "This might be due to missing profile data or a network issue. Please try signing out and signing in again.",
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 30),
              ElevatedButton.icon(
                icon: const Icon(Icons.refresh),
                label: const Text("Try Reloading Profile"),
                onPressed: () {
                  print("HomeScreen Error UI: Try Reloading Profile pressed.");
                  // Reset state and try loading again
                  if (mounted) {
                    setState(() {
                      _isLoading = true;
                      _currentUserData = null;
                    });
                    _loadUserData();
                  }
                },
              ),
              const SizedBox(height: 10),
              TextButton(
                child: const Text("Sign Out"),
                onPressed: () async {
                  print("HomeScreen Error UI: Sign Out text button pressed.");
                  await _authService.signOut();
                  print(
                    "HomeScreen Error UI: signOut completed. StreamBuilder should handle navigation.",
                  );
                  // The StreamBuilder in App.dart will automatically handle navigation to LoginScreen
                  // when it detects the auth state change (user becomes null)
                },
              ),
            ],
          ),
        ),
      ),
    );
  }
}
