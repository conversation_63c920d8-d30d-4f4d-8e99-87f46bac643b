// lib/screens/customer/checkout_screen.dart
// ignore_for_file: unused_import

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:uuid/uuid.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutterwave_standard/models/responses/charge_response.dart';

import 'package:agrimart/providers/cart_provider.dart';
import 'package:agrimart/services/payment_service.dart';
import 'package:agrimart/services/firestore_service.dart';
import 'package:agrimart/services/auth_service.dart';
import 'package:agrimart/models/order_model.dart';
import 'package:agrimart/models/user_model.dart' as AppUser; // Alias
import 'package:agrimart/models/cart_item_model.dart';

class CheckoutScreen extends StatefulWidget {
  const CheckoutScreen({super.key});

  @override
  State<CheckoutScreen> createState() => _CheckoutScreenState();
}

class _CheckoutScreenState extends State<CheckoutScreen> {
  final _formKey = GlobalKey<FormState>();
  final _addressController = TextEditingController(
    text: "Plot 123, <PERSON><PERSON>, <PERSON>",
  ); // Sample
  final _phoneController = TextEditingController(
    text: "07XXXXXXXX",
  ); // Sample, user should fill

  bool _isLoading = false;
  final PaymentService _paymentService = PaymentService();
  final FirestoreService _firestoreService = FirestoreService();
  final AuthService _authService = AuthService();
  final Uuid _uuid = Uuid();

  AppUser.UserModel? _currentUserData;

  @override
  void initState() {
    super.initState();
    _loadUserData();
  }

  Future<void> _loadUserData() async {
    final firebaseUser = _authService.currentUser;
    if (firebaseUser != null) {
      _currentUserData = await _firestoreService.getUserData(firebaseUser.uid);
      if (_currentUserData?.displayName != null &&
          _phoneController.text.isEmpty) {
        // Pre-fill phone if available from user profile (you'd need to add phone to UserModel)
        // _phoneController.text = _currentUserData.phoneNumber ?? "";
      }
      if (mounted) setState(() {});
    }
  }

  @override
  void dispose() {
    _addressController.dispose();
    _phoneController.dispose();
    super.dispose();
  }

  Future<void> _initiatePayment(CartProvider cart) async {
    if (!_formKey.currentState!.validate()) {
      return;
    }
    if (_currentUserData == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text("User data not loaded. Please try again."),
        ),
      );
      return;
    }

    setState(() => _isLoading = true);

    final String transactionRef =
        "AGM-${_uuid.v4()}"; // Unique transaction reference for this attempt
    final double totalAmount = cart.totalAmount;

    // For prototype, directly use customer's display name and email from auth
    final String customerName =
        _currentUserData!.displayName ?? "Valued Customer";
    final String customerEmail = _currentUserData!.email;
    final String customerPhoneNumber =
        _phoneController.text.trim(); // Get from form

    await _paymentService.processPayment(
      context: context,
      amount: totalAmount,
      email: customerEmail,
      phoneNumber: customerPhoneNumber, // Crucial for mobile money
      customerName: customerName,
      ourUniqueTxRef: transactionRef,
      onSuccessful: (ChargeResponse fwResponse, String returnedTxRef) async {
        print(
          "Payment successful callback: FW_TxID=${fwResponse.transactionId}, OurTxRef=$transactionRef, ReturnedTxRef=$returnedTxRef",
        );
        // Create order in Firestore
        final List<OrderItemModel> orderItems =
            cart.items.values.map((cartItem) {
              return OrderItemModel(
                productId: cartItem.product.id!,
                productName: cartItem.product.title,
                quantity: cartItem.quantity,
                priceAtPurchase: cartItem.product.price,
                imageUrl:
                    cartItem
                        .product
                        .imageUrl, // Local path, for prototype consistency
                vendorId: cartItem.product.vendorId,
              );
            }).toList();

        final order = OrderModel(
          customerId: _currentUserData!.uid,
          customerName: customerName,
          customerEmail: customerEmail,
          items: orderItems,
          totalAmount: totalAmount,
          orderStatus:
              OrderStatus
                  .pendingConfirmation, // "On hold" until delivery confirmation
          paymentGateway: "Flutterwave",
          transactionRef: transactionRef, // Our internally generated ref
          paymentGatewayRef:
              fwResponse.transactionId, // Flutterwave's transaction ID
          createdAt: Timestamp.now(),
          updatedAt: Timestamp.now(),
          shippingAddress: _addressController.text.trim(),
        );

        try {
          await _firestoreService.createOrder(order);
          cart.clearCart(); // Clear cart after successful order creation
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text("Payment Successful! Order placed."),
                backgroundColor: Colors.green,
              ),
            );
            Navigator.of(
              context,
            ).popUntil((route) => route.isFirst); // Go back to customer home
          }
        } catch (e) {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text("Order creation failed: $e"),
                backgroundColor: Colors.red,
              ),
            );
          }
        } finally {
          if (mounted) setState(() => _isLoading = false);
        }
      },
      onError: (String errorMessage, String txRef) async {
        print("Payment error callback: Error=$errorMessage, TxRef=$txRef");
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text("Payment failed: $errorMessage"),
              backgroundColor: Colors.red,
            ),
          );
          setState(() => _isLoading = false);
        }
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final cart = Provider.of<CartProvider>(context);
    if (cart.items.isEmpty && !_isLoading) {
      // Check if cart became empty while loading
      // If cart is empty, don't allow checkout (could happen if user navigates back and cart is cleared)
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted && Navigator.canPop(context)) Navigator.pop(context);
      });
      return const Scaffold(body: Center(child: Text("Your cart is empty.")));
    }

    return Scaffold(
      appBar: AppBar(title: const Text("Checkout")),
      body:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : SingleChildScrollView(
                padding: const EdgeInsets.all(16.0),
                child: Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        "Order Summary",
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 10),
                      Card(
                        child: ListView.builder(
                          shrinkWrap: true,
                          physics: const NeverScrollableScrollPhysics(),
                          itemCount: cart.items.length,
                          itemBuilder: (ctx, i) {
                            final cartItem = cart.items.values.toList()[i];
                            return ListTile(
                              title: Text(cartItem.product.title),
                              subtitle: Text(
                                '${cartItem.quantity} x TZS ${cartItem.product.price.toStringAsFixed(0)}',
                              ),
                              trailing: Text(
                                'TZS ${cartItem.subtotal.toStringAsFixed(0)}',
                              ),
                            );
                          },
                        ),
                      ),
                      const SizedBox(height: 10),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          const Text(
                            "Total:",
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          Text(
                            "TZS ${cart.totalAmount.toStringAsFixed(0)}",
                            style: const TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: Colors.green,
                            ),
                          ),
                        ],
                      ),
                      const Divider(height: 30),

                      const Text(
                        "Shipping Details",
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 10),
                      TextFormField(
                        controller: _addressController,
                        decoration: const InputDecoration(
                          labelText: "Shipping Address",
                          border: OutlineInputBorder(),
                        ),
                        validator:
                            (value) =>
                                value!.isEmpty
                                    ? 'Please enter shipping address'
                                    : null,
                        maxLines: 2,
                      ),
                      const SizedBox(height: 15),
                      TextFormField(
                        controller: _phoneController,
                        decoration: const InputDecoration(
                          labelText: "Phone Number (for delivery & M-Pesa)",
                          border: OutlineInputBorder(),
                          prefixText: "+255 ",
                        ),
                        keyboardType: TextInputType.phone,
                        validator: (value) {
                          if (value!.isEmpty)
                            return 'Please enter phone number';
                          // Basic Tanzanian phone number validation (e.g., starts with 6 or 7, 9 digits)
                          if (!RegExp(
                            r"^[67]\d{8}$",
                          ).hasMatch(value.replaceAll(RegExp(r'\s+'), ''))) {
                            // remove spaces before validation
                            return 'Enter a valid TZ mobile number (e.g. 712345678)';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 30),
                      ElevatedButton(
                        style: ElevatedButton.styleFrom(
                          minimumSize: const Size(double.infinity, 50),
                          backgroundColor: Theme.of(context).primaryColor,
                          foregroundColor: Colors.white,
                          textStyle: const TextStyle(fontSize: 18),
                        ),
                        onPressed:
                            cart.items.isEmpty
                                ? null
                                : () => _initiatePayment(cart),
                        child: const Text("Proceed to Pay"),
                      ),
                    ],
                  ),
                ),
              ),
    );
  }
}
