// lib/screens/customer/customer_home_screen.dart
import 'package:agrimart/screens/customer/customer_order_list_screen.dart';
// import 'package:agrimart/services/auth_service.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:agrimart/models/product_model.dart';
import 'package:agrimart/services/firestore_service.dart';
import 'package:agrimart/screens/customer/product_detail_screen.dart';
import 'package:agrimart/screens/customer/cart_screen.dart';
import 'package:agrimart/screens/test/payment_test_screen.dart';
import 'package:agrimart/screens/admin/migration_screen.dart';
import 'package:agrimart/screens/test/transaction_workflow_test_screen.dart';
import 'package:agrimart/providers/cart_provider.dart';
import 'package:agrimart/models/user_model.dart' as AppUser;
import 'package:agrimart/widgets/product_card.dart';
import 'package:agrimart/widgets/modern_app_bar.dart';
import 'package:agrimart/widgets/user_profile_avatar.dart';
import 'package:agrimart/screens/profile/profile_screen.dart';
import 'package:agrimart/main.dart';

class CustomerHomeScreen extends StatefulWidget {
  final AppUser.UserModel currentUserData; // <-- ADD THIS

  const CustomerHomeScreen({
    super.key,
    required this.currentUserData,
  }); // <-- UPDATE CONSTRUCTOR

  @override
  State<CustomerHomeScreen> createState() => _CustomerHomeScreenState();
}

class _CustomerHomeScreenState extends State<CustomerHomeScreen> {
  final FirestoreService _firestoreService = FirestoreService();
  String _selectedCategory = 'All'; // Default category
  final List<String> _categories = [
    'All',
    'Honey',
    'Maize',
    'Sorghum',
  ]; // Add more relevant categories

  @override
  Widget build(BuildContext context) {
    print(
      "CustomerHomeScreen: build() called for user: ${widget.currentUserData.displayName}",
    );
    final cart = Provider.of<CartProvider>(context, listen: false);

    return Scaffold(
      appBar: AgrimartAppBar(
        title: "Agrimart",
        actions: [
          Consumer<CartProvider>(
            builder:
                (_, cartData, ch) => Badge(
                  label: Text(cartData.itemCount.toString()),
                  isLabelVisible: cartData.itemCount > 0,
                  child: ch,
                ),
            child: IconButton(
              icon: const Icon(Icons.shopping_cart),
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(builder: (_) => const CartScreen()),
                );
              },
            ),
          ),
          IconButton(
            icon: const Icon(Icons.history),
            tooltip: "My Orders",
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (_) => const CustomerOrderListScreen(),
                ),
              );
            },
          ),
          PopupMenuButton<String>(
            icon: const Icon(Icons.more_vert),
            onSelected: (value) {
              switch (value) {
                case 'test_payment':
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder:
                          (_) => PaymentTestScreen(
                            currentUser: widget.currentUserData,
                          ),
                    ),
                  );
                  break;
                case 'migration':
                  Navigator.push(
                    context,
                    MaterialPageRoute(builder: (_) => const MigrationScreen()),
                  );
                  break;
                case 'workflow_test':
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder:
                          (_) => TransactionWorkflowTestScreen(
                            currentUser: widget.currentUserData,
                          ),
                    ),
                  );
                  break;
              }
            },
            itemBuilder:
                (context) => [
                  const PopupMenuItem(
                    value: 'test_payment',
                    child: Row(
                      children: [
                        Icon(Icons.payment),
                        SizedBox(width: 8),
                        Text('Test Payment'),
                      ],
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'migration',
                    child: Row(
                      children: [
                        Icon(Icons.build),
                        SizedBox(width: 8),
                        Text('Run Migration'),
                      ],
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'workflow_test',
                    child: Row(
                      children: [
                        Icon(Icons.science),
                        SizedBox(width: 8),
                        Text('Test Workflow'),
                      ],
                    ),
                  ),
                ],
          ),
          Padding(
            padding: const EdgeInsets.only(right: 8.0),
            child: UserProfileAvatar(
              user: widget.currentUserData,
              size: 36,
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (_) => ProfileScreen(user: widget.currentUserData),
                  ),
                );
              },
            ),
          ),
        ],
      ),
      body: Column(
        children: [
          // Welcome Section
          Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
            decoration: const BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [AppColors.primaryGreenSoft, AppColors.surfaceLight],
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  'Welcome, ${widget.currentUserData.displayName ?? 'Customer'}! 👋',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: AppColors.textPrimary,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 2),
                const Text(
                  'Discover fresh, quality agricultural products',
                  style: TextStyle(
                    fontSize: 12,
                    color: AppColors.textSecondary,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),

          // Category Filter Bar
          Container(
            padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                const Text(
                  'Shop by Category',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: AppColors.textPrimary,
                  ),
                ),
                const SizedBox(height: 6),
                SizedBox(
                  height: 32,
                  child: ListView.builder(
                    scrollDirection: Axis.horizontal,
                    itemCount: _categories.length,
                    itemBuilder: (context, index) {
                      final category = _categories[index];
                      final isSelected = _selectedCategory == category;
                      return Padding(
                        padding: const EdgeInsets.only(right: 6.0),
                        child: FilterChip(
                          label: Text(
                            category,
                            style: TextStyle(
                              fontSize: 11,
                              color:
                                  isSelected
                                      ? AppColors.textOnPrimary
                                      : AppColors.textSecondary,
                              fontWeight:
                                  isSelected
                                      ? FontWeight.w600
                                      : FontWeight.normal,
                            ),
                          ),
                          selected: isSelected,
                          onSelected: (selected) {
                            if (selected) {
                              setState(() {
                                _selectedCategory = category;
                              });
                            }
                          },
                          backgroundColor: AppColors.surfaceLight,
                          selectedColor: AppColors.primaryGreen,
                          checkmarkColor: AppColors.textOnPrimary,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(16),
                            side: BorderSide(
                              color:
                                  isSelected
                                      ? AppColors.primaryGreen
                                      : AppColors.divider,
                            ),
                          ),
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 2,
                          ),
                        ),
                      );
                    },
                  ),
                ),
              ],
            ),
          ),
          Expanded(
            child: StreamBuilder<List<ProductModel>>(
              stream:
                  _selectedCategory.toLowerCase() == 'all'
                      ? _firestoreService.getAllProducts()
                      : _firestoreService.getProductsByCategory(
                        _selectedCategory,
                      ),
              builder: (context, snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting) {
                  return const Center(child: CircularProgressIndicator());
                }
                if (snapshot.hasError) {
                  return Center(child: Text("Error: ${snapshot.error}"));
                }
                if (!snapshot.hasData || snapshot.data!.isEmpty) {
                  return Center(
                    child: Text(
                      "No products found${_selectedCategory.toLowerCase() != 'all' ? ' in $_selectedCategory' : ''}.",
                      style: const TextStyle(fontSize: 16),
                    ),
                  );
                }

                final products = snapshot.data!;
                return GridView.builder(
                  padding: const EdgeInsets.all(16.0),
                  gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 2,
                    childAspectRatio:
                        0.75, // Slightly taller cards to prevent overflow
                    crossAxisSpacing: 12,
                    mainAxisSpacing: 12,
                  ),
                  itemCount: products.length,
                  itemBuilder: (context, index) {
                    final product = products[index];
                    return ProductCard(
                      product: product,
                      onTap: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder:
                                (_) => ProductDetailScreen(product: product),
                          ),
                        );
                      },
                      onAddToCart: () {
                        cart.addItem(product);
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text('${product.title} added to cart!'),
                            duration: const Duration(seconds: 2),
                            backgroundColor: AppColors.success,
                            behavior: SnackBarBehavior.floating,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(10),
                            ),
                          ),
                        );
                      },
                    );
                  },
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}
