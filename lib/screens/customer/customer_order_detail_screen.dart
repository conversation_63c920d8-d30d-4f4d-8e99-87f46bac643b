// lib/screens/customer/customer_order_detail_screen.dart
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:agrimart/services/firestore_service.dart';
import 'package:agrimart/services/auth_service.dart';
import 'package:agrimart/models/order_model.dart';
import 'package:agrimart/models/user_model.dart'
    as AppUser; // For current user data if needed for dispute
import 'package:intl/intl.dart';
import 'dart:io'; // For local image display
import 'package:agrimart/screens/customer/rate_vendor_screen.dart';
import 'package:agrimart/main.dart';

class CustomerOrderDetailScreen extends StatefulWidget {
  final String orderId;
  const CustomerOrderDetailScreen({super.key, required this.orderId});

  @override
  State<CustomerOrderDetailScreen> createState() =>
      _CustomerOrderDetailScreenState();
}

class _CustomerOrderDetailScreenState extends State<CustomerOrderDetailScreen> {
  final _disputeReasonController = TextEditingController();
  final _disputeCommentsController = TextEditingController();
  AppUser.UserModel? _currentUserData; // To get customer name for dispute

  @override
  void initState() {
    super.initState();
    _loadUserData();
  }

  Future<void> _loadUserData() async {
    final authService = Provider.of<AuthService>(context, listen: false);
    final firestoreService = Provider.of<FirestoreService>(
      context,
      listen: false,
    );
    if (authService.currentUser != null) {
      _currentUserData = await firestoreService.getUserData(
        authService.currentUser!.uid,
      );
      if (mounted) setState(() {});
    }
  }

  @override
  void dispose() {
    _disputeReasonController.dispose();
    _disputeCommentsController.dispose();
    super.dispose();
  }

  Widget _buildProductImage(String imagePath) {
    File imageFile = File(imagePath);
    if (imagePath.isNotEmpty && imageFile.existsSync()) {
      return Image.file(imageFile, width: 50, height: 50, fit: BoxFit.cover);
    }
    return Container(
      width: 50,
      height: 50,
      color: Colors.grey[200],
      child: const Icon(Icons.image_not_supported),
    );
  }

  void _showRaiseDisputeDialog(
    BuildContext context,
    OrderModel order,
    FirestoreService fs,
  ) {
    final formKey = GlobalKey<FormState>();
    _disputeReasonController.clear();
    _disputeCommentsController.clear();

    showDialog(
      context: context,
      builder:
          (ctx) => AlertDialog(
            title: const Text("Raise a Dispute"),
            content: Form(
              key: formKey,
              child: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    TextFormField(
                      controller: _disputeReasonController,
                      decoration: const InputDecoration(
                        labelText: "Reason for Dispute",
                      ),
                      validator:
                          (value) =>
                              value!.isEmpty ? "Please enter a reason" : null,
                    ),
                    const SizedBox(height: 10),
                    TextFormField(
                      controller: _disputeCommentsController,
                      decoration: const InputDecoration(
                        labelText: "Comments (optional)",
                      ),
                      maxLines: 3,
                    ),
                  ],
                ),
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(ctx).pop(),
                child: const Text("Cancel"),
              ),
              ElevatedButton(
                onPressed: () async {
                  if (formKey.currentState!.validate()) {
                    if (_currentUserData == null) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text(
                            "User data not loaded, cannot raise dispute.",
                          ),
                        ),
                      );
                      Navigator.of(ctx).pop();
                      return;
                    }
                    List<String> vendorIds =
                        order.items
                            .map((item) => item.vendorId)
                            .toSet()
                            .toList(); // Get unique vendor IDs

                    final dispute = DisputeModel(
                      orderId: order.id!,
                      customerId: _currentUserData!.uid,
                      vendorIds: vendorIds,
                      reason: _disputeReasonController.text,
                      customerComments: _disputeCommentsController.text,
                      createdAt: Timestamp.now(),
                    );
                    try {
                      await fs.createDisputeWithHold(dispute);
                      // Order status is updated to 'disputed' and transaction held
                      Navigator.of(ctx).pop(); // Close dialog
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text(
                            "Dispute raised successfully. Transaction is now held.",
                          ),
                        ),
                      );
                    } catch (e) {
                      Navigator.of(ctx).pop();
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(content: Text("Failed to raise dispute: $e")),
                      );
                    }
                  }
                },
                child: const Text("Submit Dispute"),
              ),
            ],
          ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final firestoreService = Provider.of<FirestoreService>(
      context,
      listen: false,
    );

    return Scaffold(
      backgroundColor: AppColors.scaffoldBackground,
      appBar: AppBar(
        title: const Text("Order Details"),
        backgroundColor: AppColors.primaryGreen,
        foregroundColor: AppColors.textOnPrimary,
        elevation: 0,
      ),
      body: StreamBuilder<OrderModel>(
        stream: firestoreService.getOrderStream(widget.orderId),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(child: CircularProgressIndicator());
          }
          if (snapshot.hasError || !snapshot.hasData) {
            return Center(
              child: Text("Error loading order: ${snapshot.error}"),
            );
          }
          final order = snapshot.data!;

          bool canConfirmDelivery =
              order.orderStatus == OrderStatus.pendingConfirmation ||
              order.orderStatus == OrderStatus.shipped;
          bool canRaiseDispute =
              order.orderStatus == OrderStatus.pendingConfirmation ||
              order.orderStatus == OrderStatus.shipped;
          bool alreadyDelivered =
              order.orderStatus == OrderStatus.delivered ||
              order.orderStatus == OrderStatus.completed ||
              order.orderStatus == OrderStatus.resolvedDispute;
          bool canRateVendors =
              order.orderStatus == OrderStatus.delivered ||
              order.orderStatus == OrderStatus.completed;

          return SingleChildScrollView(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Order Header Card
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    color: AppColors.cardBackground,
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: [
                      BoxShadow(
                        color: AppColors.primaryGreen.withValues(alpha: 0.1),
                        blurRadius: 10,
                        offset: const Offset(0, 4),
                      ),
                    ],
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Container(
                            padding: const EdgeInsets.all(8),
                            decoration: BoxDecoration(
                              color: _getStatusColor(
                                order.orderStatus,
                              ).withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Icon(
                              _getStatusIcon(order.orderStatus),
                              color: _getStatusColor(order.orderStatus),
                              size: 20,
                            ),
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  "Order #${order.id?.substring(0, 8) ?? 'Unknown'}",
                                  style: TextStyle(
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                    color: AppColors.textPrimary,
                                  ),
                                ),
                                const SizedBox(height: 4),
                                Text(
                                  DateFormat.yMMMd().add_jm().format(
                                    order.createdAt.toDate(),
                                  ),
                                  style: TextStyle(
                                    fontSize: 14,
                                    color: AppColors.textSecondary,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),

                      const SizedBox(height: 16),

                      // Status Row
                      Row(
                        children: [
                          Expanded(
                            child: _buildStatusChip(
                              "Order Status",
                              _getStatusText(order.orderStatus),
                              _getStatusColor(order.orderStatus),
                            ),
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: _buildStatusChip(
                              "Transaction",
                              _getTransactionStatusText(
                                _getEffectiveTransactionStatus(order),
                              ),
                              _getTransactionStatusColor(
                                _getEffectiveTransactionStatus(order),
                              ),
                            ),
                          ),
                        ],
                      ),

                      const SizedBox(height: 16),

                      // Order Info
                      Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: AppColors.surfaceLight,
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Column(
                          children: [
                            _buildInfoRow(
                              "Total Amount",
                              "TZS ${order.totalAmount.toStringAsFixed(0)}",
                            ),
                            const SizedBox(height: 8),
                            _buildInfoRow(
                              "Shipping Address",
                              order.shippingAddress,
                            ),
                            const SizedBox(height: 8),
                            _buildInfoRow(
                              "Items",
                              "${order.items.length} item${order.items.length > 1 ? 's' : ''}",
                            ),
                          ],
                        ),
                      ),

                      // Confirmation Status
                      if (order.orderStatus == OrderStatus.shipped ||
                          order.orderStatus == OrderStatus.delivered ||
                          order.orderStatus == OrderStatus.completed) ...[
                        const SizedBox(height: 16),
                        Row(
                          children: [
                            Expanded(
                              child: _buildConfirmationStatus(
                                "Vendor Shipped",
                                order.vendorShipmentConfirmed,
                                order.vendorConfirmedAt,
                              ),
                            ),
                            const SizedBox(width: 12),
                            Expanded(
                              child: _buildConfirmationStatus(
                                "You Confirmed",
                                order.customerDeliveryConfirmed,
                                order.customerConfirmedAt,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ],
                  ),
                ),

                const SizedBox(height: 20),
                // Items Card
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    color: AppColors.cardBackground,
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: [
                      BoxShadow(
                        color: AppColors.primaryGreen.withValues(alpha: 0.1),
                        blurRadius: 10,
                        offset: const Offset(0, 4),
                      ),
                    ],
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(
                            Icons.shopping_cart,
                            color: AppColors.primaryGreen,
                            size: 20,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            "Order Items",
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: AppColors.textPrimary,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      ...order.items.map((item) => _buildItemCard(item)),
                    ],
                  ),
                ),

                const SizedBox(height: 20),
                if (order.orderStatus == OrderStatus.disputed) ...[
                  const SizedBox(height: 10),
                  Text(
                    "Dispute Information:",
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  Text("Reason: ${order.disputeReason ?? 'N/A'}"),
                  Text("Comments: ${order.disputeComments ?? 'N/A'}"),
                  if (order.adminResolutionNotes != null)
                    Text("Admin Notes: ${order.adminResolutionNotes}"),
                  const SizedBox(height: 10),
                ],
                const SizedBox(height: 20),
                if (canConfirmDelivery)
                  ElevatedButton.icon(
                    icon: const Icon(Icons.check_circle_outline),
                    label: const Text("Confirm Delivery"),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                      minimumSize: const Size(double.infinity, 45),
                    ),
                    onPressed: () async {
                      try {
                        await firestoreService.confirmCustomerDelivery(
                          order.id!,
                          order.customerId,
                        );
                        if (mounted) {
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(
                              content: Text(
                                "Delivery Confirmed! Transaction will be processed.",
                              ),
                            ),
                          );
                        }
                      } catch (e) {
                        if (mounted) {
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text("Failed to confirm delivery: $e"),
                            ),
                          );
                        }
                      }
                    },
                  ),
                if (alreadyDelivered &&
                    order.orderStatus != OrderStatus.disputed)
                  Center(
                    child: Text(
                      "This order has been ${orderStatusToString(order.orderStatus)}.",
                      style: const TextStyle(
                        color: Colors.green,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),

                const SizedBox(height: 10),

                // Rate Vendor Button
                if (canRateVendors)
                  ElevatedButton.icon(
                    icon: const Icon(Icons.star_rate),
                    label: const Text("Rate Vendors"),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.premiumGold,
                      foregroundColor: Colors.white,
                      minimumSize: const Size(double.infinity, 45),
                    ),
                    onPressed:
                        () => _showRateVendorsDialog(
                          context,
                          order,
                          firestoreService,
                        ),
                  ),

                const SizedBox(height: 10),
                if (canRaiseDispute)
                  OutlinedButton.icon(
                    icon: const Icon(Icons.report_problem_outlined),
                    label: const Text("Raise a Dispute"),
                    style: OutlinedButton.styleFrom(
                      foregroundColor: Colors.orange[700],
                      minimumSize: const Size(double.infinity, 45),
                    ),
                    onPressed:
                        () => _showRaiseDisputeDialog(
                          context,
                          order,
                          firestoreService,
                        ),
                  ),

                if (order.orderStatus == OrderStatus.disputed)
                  Center(
                    child: Text(
                      "Your dispute is being reviewed.",
                      style: TextStyle(
                        color: Colors.orange[700],
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
              ],
            ),
          );
        },
      ),
    );
  }

  String _getTransactionStatusText(TransactionStatus status) {
    switch (status) {
      case TransactionStatus.held:
        return 'Held in Escrow';
      case TransactionStatus.released:
        return 'Released to Vendor';
      case TransactionStatus.refunded:
        return 'Refunded to Customer';
      case TransactionStatus.disputed:
        return 'Held (Disputed)';
    }
  }

  void _showRateVendorsDialog(
    BuildContext context,
    OrderModel order,
    FirestoreService firestoreService,
  ) {
    // Get unique vendors from the order
    final uniqueVendors = <String, String>{};
    for (final item in order.items) {
      uniqueVendors[item.vendorId] =
          item.vendorId; // In real app, you'd get vendor name
    }

    if (uniqueVendors.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('No vendors found in this order')),
      );
      return;
    }

    showDialog(
      context: context,
      builder:
          (ctx) => AlertDialog(
            title: const Text('Rate Vendors'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Text('Choose a vendor to rate:'),
                const SizedBox(height: 16),
                ...uniqueVendors.entries.map(
                  (entry) => ListTile(
                    leading: const Icon(Icons.store),
                    title: Text('Vendor ${entry.key.substring(0, 8)}...'),
                    trailing: const Icon(Icons.arrow_forward_ios),
                    onTap: () {
                      Navigator.of(ctx).pop();
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder:
                              (_) => RateVendorScreen(
                                vendorId: entry.key,
                                vendorName:
                                    'Vendor ${entry.key.substring(0, 8)}...',
                                currentUser: _currentUserData!,
                                orderId: order.id,
                              ),
                        ),
                      );
                    },
                  ),
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(ctx).pop(),
                child: const Text('Cancel'),
              ),
            ],
          ),
    );
  }

  Widget _buildStatusChip(String label, String value, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: 10,
              color: AppColors.textSecondary,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 2),
          Text(
            value,
            style: TextStyle(
              fontSize: 12,
              color: color,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 100,
          child: Text(
            label,
            style: TextStyle(
              fontSize: 14,
              color: AppColors.textSecondary,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Text(
            value,
            style: TextStyle(
              fontSize: 14,
              color: AppColors.textPrimary,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildConfirmationStatus(
    String label,
    bool confirmed,
    Timestamp? timestamp,
  ) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color:
            confirmed
                ? AppColors.success.withValues(alpha: 0.1)
                : AppColors.textSecondary.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color:
              confirmed
                  ? AppColors.success.withValues(alpha: 0.3)
                  : AppColors.textSecondary.withValues(alpha: 0.3),
        ),
      ),
      child: Column(
        children: [
          Icon(
            confirmed ? Icons.check_circle : Icons.radio_button_unchecked,
            size: 20,
            color: confirmed ? AppColors.success : AppColors.textSecondary,
          ),
          const SizedBox(height: 4),
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: confirmed ? AppColors.success : AppColors.textSecondary,
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
          if (confirmed && timestamp != null) ...[
            const SizedBox(height: 2),
            Text(
              DateFormat.MMMd().add_jm().format(timestamp.toDate()),
              style: TextStyle(fontSize: 10, color: AppColors.textSecondary),
              textAlign: TextAlign.center,
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildItemCard(OrderItemModel item) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.surfaceLight,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.divider),
      ),
      child: Row(
        children: [
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              color: AppColors.primaryGreen.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child:
                item.imageUrl.isNotEmpty
                    ? ClipRRect(
                      borderRadius: BorderRadius.circular(8),
                      child: Image.file(
                        File(item.imageUrl),
                        width: 60,
                        height: 60,
                        fit: BoxFit.cover,
                        errorBuilder:
                            (context, error, stackTrace) => Icon(
                              Icons.image_not_supported,
                              color: AppColors.textSecondary,
                            ),
                      ),
                    )
                    : Icon(
                      Icons.shopping_bag,
                      color: AppColors.primaryGreen,
                      size: 24,
                    ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  item.productName,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: AppColors.textPrimary,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  "${item.quantity} x TZS ${item.priceAtPurchase.toStringAsFixed(0)}",
                  style: TextStyle(
                    fontSize: 14,
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ),
          ),
          Text(
            "TZS ${(item.quantity * item.priceAtPurchase).toStringAsFixed(0)}",
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: AppColors.primaryGreen,
            ),
          ),
        ],
      ),
    );
  }

  Color _getStatusColor(OrderStatus status) {
    switch (status) {
      case OrderStatus.pendingConfirmation:
        return Colors.orange;
      case OrderStatus.shipped:
        return Colors.blue;
      case OrderStatus.delivered:
        return AppColors.success;
      case OrderStatus.completed:
        return AppColors.primaryGreen;
      case OrderStatus.disputed:
        return AppColors.error;
      case OrderStatus.cancelledByCustomer:
      case OrderStatus.cancelledByVendor:
        return Colors.grey;
      default:
        return AppColors.textSecondary;
    }
  }

  IconData _getStatusIcon(OrderStatus status) {
    switch (status) {
      case OrderStatus.pendingConfirmation:
        return Icons.hourglass_empty;
      case OrderStatus.shipped:
        return Icons.local_shipping;
      case OrderStatus.delivered:
        return Icons.check_circle;
      case OrderStatus.completed:
        return Icons.verified;
      case OrderStatus.disputed:
        return Icons.report_problem;
      case OrderStatus.cancelledByCustomer:
      case OrderStatus.cancelledByVendor:
        return Icons.cancel;
      default:
        return Icons.info;
    }
  }

  String _getStatusText(OrderStatus status) {
    switch (status) {
      case OrderStatus.pendingConfirmation:
        return "Pending";
      case OrderStatus.shipped:
        return "Shipped";
      case OrderStatus.delivered:
        return "Delivered";
      case OrderStatus.completed:
        return "Completed";
      case OrderStatus.disputed:
        return "Disputed";
      case OrderStatus.cancelledByCustomer:
        return "Cancelled";
      case OrderStatus.cancelledByVendor:
        return "Cancelled";
      default:
        return orderStatusToString(status);
    }
  }

  Color _getTransactionStatusColor(TransactionStatus status) {
    switch (status) {
      case TransactionStatus.held:
        return Colors.orange;
      case TransactionStatus.released:
        return AppColors.success;
      case TransactionStatus.refunded:
        return Colors.blue;
      case TransactionStatus.disputed:
        return AppColors.error;
    }
  }

  // Helper method to get effective transaction status for existing orders
  TransactionStatus _getEffectiveTransactionStatus(OrderModel order) {
    return FirestoreService.getTransactionStatusFromOrderStatus(
      order.orderStatus,
      order.transactionStatus,
    );
  }
}
