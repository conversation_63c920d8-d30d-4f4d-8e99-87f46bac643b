// lib/screens/customer/customer_order_list_screen.dart
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:agrimart/services/auth_service.dart';
import 'package:agrimart/services/firestore_service.dart';
import 'package:agrimart/models/order_model.dart';
import 'package:agrimart/screens/customer/customer_order_detail_screen.dart';
import 'package:agrimart/main.dart';
import 'package:intl/intl.dart';

class CustomerOrderListScreen extends StatelessWidget {
  const CustomerOrderListScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final authService = Provider.of<AuthService>(context, listen: false);
    final firestoreService = Provider.of<FirestoreService>(
      context,
      listen: false,
    );
    final currentUser = authService.currentUser;

    if (currentUser == null) {
      return Scaffold(
        backgroundColor: AppColors.scaffoldBackground,
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.login, size: 64, color: AppColors.textSecondary),
              const SizedBox(height: 16),
              Text(
                "Please log in to see your orders",
                style: TextStyle(fontSize: 18, color: AppColors.textPrimary),
              ),
            ],
          ),
        ),
      );
    }

    return Scaffold(
      backgroundColor: AppColors.scaffoldBackground,
      appBar: AppBar(
        title: const Text("My Orders"),
        backgroundColor: AppColors.primaryGreen,
        foregroundColor: AppColors.textOnPrimary,
        elevation: 0,
      ),
      body: StreamBuilder<List<OrderModel>>(
        stream: firestoreService.getCustomerOrders(currentUser.uid),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(child: CircularProgressIndicator());
          }
          if (snapshot.hasError) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.error_outline, size: 64, color: AppColors.error),
                  const SizedBox(height: 16),
                  Text(
                    "Error loading orders",
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: AppColors.textPrimary,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    "Please try again later",
                    style: TextStyle(color: AppColors.textSecondary),
                  ),
                ],
              ),
            );
          }
          if (!snapshot.hasData || snapshot.data!.isEmpty) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.shopping_bag_outlined,
                    size: 64,
                    color: AppColors.textSecondary,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    "No orders yet",
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: AppColors.textPrimary,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    "Your orders will appear here",
                    style: TextStyle(color: AppColors.textSecondary),
                  ),
                ],
              ),
            );
          }

          final orders = snapshot.data!;
          return ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: orders.length,
            itemBuilder: (context, index) {
              final order = orders[index];
              return _buildOrderCard(context, order);
            },
          );
        },
      ),
    );
  }

  Widget _buildOrderCard(BuildContext context, OrderModel order) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: AppColors.cardBackground,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: AppColors.primaryGreen.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(16),
          onTap: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (_) => CustomerOrderDetailScreen(orderId: order.id!),
              ),
            );
          },
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header Row
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: _getStatusColor(
                          order.orderStatus,
                        ).withOpacity(0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Icon(
                        _getStatusIcon(order.orderStatus),
                        color: _getStatusColor(order.orderStatus),
                        size: 20,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            "Order #${order.id?.substring(0, 8) ?? 'Unknown'}",
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: AppColors.textPrimary,
                            ),
                          ),
                          const SizedBox(height: 2),
                          Text(
                            DateFormat.yMMMd().add_jm().format(
                              order.createdAt.toDate(),
                            ),
                            style: TextStyle(
                              fontSize: 12,
                              color: AppColors.textSecondary,
                            ),
                          ),
                        ],
                      ),
                    ),
                    Icon(Icons.chevron_right, color: AppColors.textSecondary),
                  ],
                ),

                const SizedBox(height: 16),

                // Status and Transaction Info
                Row(
                  children: [
                    Expanded(
                      child: _buildInfoChip(
                        "Status",
                        _getStatusText(order.orderStatus),
                        _getStatusColor(order.orderStatus),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: _buildInfoChip(
                        "Transaction",
                        _getTransactionStatusText(
                          _getEffectiveTransactionStatus(order),
                        ),
                        _getTransactionStatusColor(
                          _getEffectiveTransactionStatus(order),
                        ),
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 16),

                // Items Summary
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: AppColors.surfaceLight,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.shopping_cart,
                        size: 16,
                        color: AppColors.textSecondary,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        "${order.items.length} item${order.items.length > 1 ? 's' : ''}",
                        style: TextStyle(
                          fontSize: 14,
                          color: AppColors.textSecondary,
                        ),
                      ),
                      const Spacer(),
                      Text(
                        "TZS ${order.totalAmount.toStringAsFixed(0)}",
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: AppColors.primaryGreen,
                        ),
                      ),
                    ],
                  ),
                ),

                // Confirmation Status (if applicable)
                if (order.orderStatus == OrderStatus.shipped ||
                    order.orderStatus == OrderStatus.delivered ||
                    order.orderStatus == OrderStatus.completed) ...[
                  const SizedBox(height: 12),
                  Row(
                    children: [
                      _buildConfirmationStatus(
                        "Vendor Shipped",
                        order.vendorShipmentConfirmed,
                      ),
                      const SizedBox(width: 16),
                      _buildConfirmationStatus(
                        "You Confirmed",
                        order.customerDeliveryConfirmed,
                      ),
                    ],
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildInfoChip(String label, String value, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: 10,
              color: AppColors.textSecondary,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 2),
          Text(
            value,
            style: TextStyle(
              fontSize: 12,
              color: color,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildConfirmationStatus(String label, bool confirmed) {
    return Row(
      children: [
        Icon(
          confirmed ? Icons.check_circle : Icons.radio_button_unchecked,
          size: 16,
          color: confirmed ? AppColors.success : AppColors.textSecondary,
        ),
        const SizedBox(width: 4),
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: confirmed ? AppColors.success : AppColors.textSecondary,
          ),
        ),
      ],
    );
  }

  Color _getStatusColor(OrderStatus status) {
    switch (status) {
      case OrderStatus.pendingConfirmation:
        return Colors.orange;
      case OrderStatus.shipped:
        return Colors.blue;
      case OrderStatus.delivered:
        return AppColors.success;
      case OrderStatus.completed:
        return AppColors.primaryGreen;
      case OrderStatus.disputed:
        return AppColors.error;
      case OrderStatus.cancelledByCustomer:
      case OrderStatus.cancelledByVendor:
        return Colors.grey;
      default:
        return AppColors.textSecondary;
    }
  }

  IconData _getStatusIcon(OrderStatus status) {
    switch (status) {
      case OrderStatus.pendingConfirmation:
        return Icons.hourglass_empty;
      case OrderStatus.shipped:
        return Icons.local_shipping;
      case OrderStatus.delivered:
        return Icons.check_circle;
      case OrderStatus.completed:
        return Icons.verified;
      case OrderStatus.disputed:
        return Icons.report_problem;
      case OrderStatus.cancelledByCustomer:
      case OrderStatus.cancelledByVendor:
        return Icons.cancel;
      default:
        return Icons.info;
    }
  }

  String _getStatusText(OrderStatus status) {
    switch (status) {
      case OrderStatus.pendingConfirmation:
        return "Pending";
      case OrderStatus.shipped:
        return "Shipped";
      case OrderStatus.delivered:
        return "Delivered";
      case OrderStatus.completed:
        return "Completed";
      case OrderStatus.disputed:
        return "Disputed";
      case OrderStatus.cancelledByCustomer:
        return "Cancelled";
      case OrderStatus.cancelledByVendor:
        return "Cancelled";
      default:
        return orderStatusToString(status);
    }
  }

  String _getTransactionStatusText(TransactionStatus status) {
    switch (status) {
      case TransactionStatus.held:
        return "Held";
      case TransactionStatus.released:
        return "Released";
      case TransactionStatus.refunded:
        return "Refunded";
      case TransactionStatus.disputed:
        return "Disputed";
    }
  }

  Color _getTransactionStatusColor(TransactionStatus status) {
    switch (status) {
      case TransactionStatus.held:
        return Colors.orange;
      case TransactionStatus.released:
        return AppColors.success;
      case TransactionStatus.refunded:
        return Colors.blue;
      case TransactionStatus.disputed:
        return AppColors.error;
    }
  }

  // Helper method to get effective transaction status for existing orders
  TransactionStatus _getEffectiveTransactionStatus(OrderModel order) {
    return FirestoreService.getTransactionStatusFromOrderStatus(
      order.orderStatus,
      order.transactionStatus,
    );
  }
}
