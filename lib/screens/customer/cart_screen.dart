// lib/screens/customer/cart_screen.dart
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:agrimart/providers/cart_provider.dart';
import 'package:agrimart/screens/customer/checkout_screen.dart';
// import 'package:agrimart/models/cart_item_model.dart'; // Not directly used in build now

class CartScreen extends StatelessWidget {
  const CartScreen({super.key});

  Widget _buildCartItemImage(String imagePath) {
    File imageFile = File(imagePath);
    if (imagePath.isNotEmpty && imageFile.existsSync()) {
      return Image.file(
        imageFile,
        height: 60,
        width: 60,
        fit: BoxFit.cover,
        errorBuilder:
            (context, error, stackTrace) =>
                const Icon(Icons.broken_image, size: 40),
      );
    } else {
      return Container(
        height: 60,
        width: 60,
        color: Colors.grey[200],
        child: const Icon(
          Icons.image_not_supported,
          size: 40,
          color: Colors.grey,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final cart = Provider.of<CartProvider>(context);
    final cartItemsList = cart.items.values.toList(); // Get list of cart items

    return Scaffold(
      appBar: AppBar(
        title: const Text('Your Cart'),
        actions: [
          if (cart.itemCount > 0)
            IconButton(
              icon: const Icon(Icons.remove_shopping_cart),
              tooltip: "Clear Cart",
              onPressed: () {
                // ... (Clear cart dialog remains the same)
                showDialog(
                  context: context,
                  builder:
                      (ctx) => AlertDialog(
                        title: const Text('Clear Cart?'),
                        content: const Text(
                          'Do you want to remove all items from your cart?',
                        ),
                        actions: <Widget>[
                          TextButton(
                            child: const Text('No'),
                            onPressed: () {
                              Navigator.of(ctx).pop();
                            },
                          ),
                          TextButton(
                            child: const Text('Yes'),
                            onPressed: () {
                              cart.clearCart();
                              Navigator.of(ctx).pop();
                            },
                          ),
                        ],
                      ),
                );
              },
            ),
        ],
      ),
      body:
          cart.items.isEmpty
              ? const Center(
                child: Text(
                  'Your cart is empty!',
                  style: TextStyle(fontSize: 18),
                ),
              )
              : Column(
                children: <Widget>[
                  Expanded(
                    child: ListView.builder(
                      itemCount: cartItemsList.length,
                      itemBuilder: (ctx, i) {
                        final cartItem = cartItemsList[i];
                        final product = cartItem.product;

                        // Unique key for Dismissible, product ID is good
                        final Key itemKey = ValueKey(product.id);

                        return Dismissible(
                          key: itemKey,
                          direction:
                              DismissDirection
                                  .endToStart, // Swipe from right to left
                          onDismissed: (direction) {
                            cart.removeItem(product.id!);
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Text(
                                  '${product.title} removed from cart.',
                                ),
                                action: SnackBarAction(
                                  label: 'UNDO',
                                  onPressed: () {
                                    // This is a bit tricky as addItem might add quantity if it was > 1 before
                                    // For simplicity, we'll just re-add it as a new item or increment if it was partially removed.
                                    // A more robust undo would require storing the exact cart state before dismissal.
                                    cart.addItem(
                                      product,
                                    ); // Re-add the product (might need to preserve original quantity)
                                  },
                                ),
                              ),
                            );
                          },
                          background: Container(
                            color: Colors.red[700],
                            alignment: Alignment.centerRight,
                            padding: const EdgeInsets.only(right: 20.0),
                            child: const Icon(
                              Icons.delete_sweep,
                              color: Colors.white,
                              size: 30,
                            ),
                          ),
                          child: Card(
                            margin: const EdgeInsets.symmetric(
                              horizontal: 15,
                              vertical: 6,
                            ),
                            child: Padding(
                              padding: const EdgeInsets.all(8),
                              child: ListTile(
                                leading: _buildCartItemImage(product.imageUrl),
                                title: Text(
                                  product.title,
                                  style: const TextStyle(
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                subtitle: Text(
                                  'TZS ${product.price.toStringAsFixed(0)}\n${product.vendorShopName}',
                                ),
                                isThreeLine: true, // To accommodate vendor name
                                trailing: SizedBox(
                                  // Keep trailing for quantity adjustment
                                  width: 117, // Can be smaller now
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.end,
                                    children: <Widget>[
                                      IconButton(
                                        icon: const Icon(
                                          Icons.remove_circle_outline,
                                          size: 22,
                                        ),
                                        padding: EdgeInsets.zero,
                                        constraints: const BoxConstraints(),
                                        onPressed: () {
                                          cart.removeSingleItem(product.id!);
                                        },
                                      ),
                                      Padding(
                                        padding: const EdgeInsets.symmetric(
                                          horizontal: 6.0,
                                        ),
                                        child: Text(
                                          '${cartItem.quantity}',
                                          style: const TextStyle(
                                            fontSize: 16,
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                      ),
                                      IconButton(
                                        icon: const Icon(
                                          Icons.add_circle_outline,
                                          size: 22,
                                        ),
                                        padding: EdgeInsets.zero,
                                        constraints: const BoxConstraints(),
                                        onPressed: () {
                                          cart.addItem(product);
                                        },
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                  // ... (Total amount and Checkout button remain the same) ...
                  if (cart.items.isNotEmpty)
                    Padding(
                      padding: const EdgeInsets.all(15.0),
                      child: Card(
                        elevation: 4,
                        child: Padding(
                          padding: const EdgeInsets.all(12.0),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: <Widget>[
                              const Text(
                                'Total:',
                                style: TextStyle(
                                  fontSize: 20,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              const Spacer(),
                              Chip(
                                label: Text(
                                  'TZS ${cart.totalAmount.toStringAsFixed(2)}',
                                  style: TextStyle(
                                    color:
                                        Theme.of(
                                          context,
                                        ).primaryTextTheme.titleLarge?.color,
                                    fontSize: 18,
                                  ),
                                ),
                                backgroundColor: Theme.of(context).primaryColor,
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  if (cart.items.isNotEmpty)
                    Padding(
                      padding: const EdgeInsets.only(
                        left: 15,
                        right: 15,
                        bottom: 20,
                        top: 5,
                      ),
                      child: ElevatedButton(
                        style: ElevatedButton.styleFrom(
                          minimumSize: const Size(double.infinity, 50),
                          backgroundColor:
                              Theme.of(context).colorScheme.secondary,
                          foregroundColor: Colors.white,
                          textStyle: const TextStyle(fontSize: 18),
                        ),
                        child: const Text('PROCEED TO CHECKOUT (Prototype)'),
                        onPressed: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (_) => const CheckoutScreen(),
                            ),
                          );
                        },
                      ),
                    ),
                ],
              ),
    );
  }
}
