import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:agrimart/services/firestore_service.dart';
import 'package:agrimart/services/auth_service.dart';
import 'package:agrimart/models/order_model.dart';
import 'package:agrimart/models/user_model.dart' as app_user;
import 'package:agrimart/main.dart';

class TransactionWorkflowTestScreen extends StatefulWidget {
  final app_user.UserModel currentUser;

  const TransactionWorkflowTestScreen({super.key, required this.currentUser});

  @override
  State<TransactionWorkflowTestScreen> createState() => _TransactionWorkflowTestScreenState();
}

class _TransactionWorkflowTestScreenState extends State<TransactionWorkflowTestScreen> {
  final FirestoreService _firestoreService = FirestoreService();
  String _testResults = '';
  bool _isRunning = false;

  Future<void> _runCompleteWorkflowTest() async {
    setState(() {
      _isRunning = true;
      _testResults = 'Starting complete transaction workflow test...\n\n';
    });

    try {
      // Step 1: Get recent orders for testing
      _updateResults('Step 1: Fetching recent orders...');
      
      if (widget.currentUser.role == app_user.UserRole.customer) {
        final orders = await _firestoreService.getCustomerOrders(widget.currentUser.uid).first;
        _updateResults('Found ${orders.length} customer orders');
        
        if (orders.isNotEmpty) {
          final testOrder = orders.first;
          await _testCustomerWorkflow(testOrder);
        } else {
          _updateResults('No orders found. Please place an order first.');
        }
      } else if (widget.currentUser.role == app_user.UserRole.vendor) {
        final orders = await _firestoreService.getVendorOrdersOptimized(widget.currentUser.uid).first;
        _updateResults('Found ${orders.length} vendor orders');
        
        if (orders.isNotEmpty) {
          final testOrder = orders.first;
          await _testVendorWorkflow(testOrder);
        } else {
          _updateResults('No orders found. Please wait for customer orders.');
        }
      }
      
      _updateResults('\n✅ Workflow test completed successfully!');
    } catch (e) {
      _updateResults('\n❌ Test failed: $e');
    } finally {
      setState(() {
        _isRunning = false;
      });
    }
  }

  Future<void> _testCustomerWorkflow(OrderModel order) async {
    _updateResults('\nTesting Customer Workflow:');
    _updateResults('Order ID: ${order.id?.substring(0, 8)}');
    _updateResults('Status: ${orderStatusToString(order.orderStatus)}');
    _updateResults('Transaction: ${transactionStatusToString(order.transactionStatus)}');
    _updateResults('Vendor Shipped: ${order.vendorShipmentConfirmed}');
    _updateResults('Customer Confirmed: ${order.customerDeliveryConfirmed}');

    // Test delivery confirmation if applicable
    if (order.orderStatus == OrderStatus.shipped && !order.customerDeliveryConfirmed) {
      _updateResults('\nTesting customer delivery confirmation...');
      try {
        await _firestoreService.confirmCustomerDelivery(order.id!, widget.currentUser.uid);
        _updateResults('✅ Customer delivery confirmation successful');
      } catch (e) {
        _updateResults('❌ Customer delivery confirmation failed: $e');
      }
    }

    // Test dispute creation if applicable
    if (order.orderStatus == OrderStatus.pendingConfirmation || order.orderStatus == OrderStatus.shipped) {
      _updateResults('\nTesting dispute creation...');
      try {
        final dispute = DisputeModel(
          orderId: order.id!,
          customerId: widget.currentUser.uid,
          vendorIds: order.vendorIds,
          reason: 'Test dispute for workflow testing',
          customerComments: 'This is a test dispute to verify the transaction holding mechanism.',
          createdAt: order.createdAt,
        );
        
        // Note: In real app, you wouldn't create test disputes
        _updateResults('✅ Dispute creation logic verified (not actually created)');
      } catch (e) {
        _updateResults('❌ Dispute creation test failed: $e');
      }
    }
  }

  Future<void> _testVendorWorkflow(OrderModel order) async {
    _updateResults('\nTesting Vendor Workflow:');
    _updateResults('Order ID: ${order.id?.substring(0, 8)}');
    _updateResults('Status: ${orderStatusToString(order.orderStatus)}');
    _updateResults('Transaction: ${transactionStatusToString(order.transactionStatus)}');
    _updateResults('Vendor Shipped: ${order.vendorShipmentConfirmed}');
    _updateResults('Customer Confirmed: ${order.customerDeliveryConfirmed}');

    // Test vendor shipment confirmation if applicable
    if (order.orderStatus == OrderStatus.pendingConfirmation && !order.vendorShipmentConfirmed) {
      _updateResults('\nTesting vendor shipment confirmation...');
      try {
        await _firestoreService.confirmVendorShipment(order.id!, widget.currentUser.uid);
        _updateResults('✅ Vendor shipment confirmation successful');
      } catch (e) {
        _updateResults('❌ Vendor shipment confirmation failed: $e');
      }
    }

    // Test analytics
    _updateResults('\nTesting vendor analytics...');
    try {
      final analytics = await _firestoreService.getVendorSalesAnalytics(widget.currentUser.uid);
      _updateResults('✅ Analytics loaded: ${analytics.totalOrders} orders, TZS ${analytics.totalRevenue.toStringAsFixed(0)} revenue');
    } catch (e) {
      _updateResults('❌ Analytics test failed: $e');
    }
  }

  void _updateResults(String message) {
    setState(() {
      _testResults += '$message\n';
    });
  }

  Future<void> _runMigration() async {
    setState(() {
      _isRunning = true;
      _testResults = 'Running migration to add transaction fields...\n\n';
    });

    try {
      await _firestoreService.migrateOrdersToIncludeVendorIds();
      _updateResults('✅ Migration completed successfully!');
    } catch (e) {
      _updateResults('❌ Migration failed: $e');
    } finally {
      setState(() {
        _isRunning = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.scaffoldBackground,
      appBar: AppBar(
        title: const Text('Transaction Workflow Test'),
        backgroundColor: AppColors.primaryGreen,
        foregroundColor: AppColors.textOnPrimary,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Transaction Workflow Testing',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: AppColors.textPrimary,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Test the complete transaction workflow including:\n'
                      '• Vendor shipment confirmation\n'
                      '• Customer delivery confirmation\n'
                      '• Transaction holding and release\n'
                      '• Dispute resolution with escrow',
                      style: TextStyle(
                        color: AppColors.textSecondary,
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: _isRunning ? null : _runCompleteWorkflowTest,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.primaryGreen,
                      foregroundColor: AppColors.textOnPrimary,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                    child: const Text('Test Workflow'),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: ElevatedButton(
                    onPressed: _isRunning ? null : _runMigration,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.orange,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                    child: const Text('Run Migration'),
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            Expanded(
              child: Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Test Results:',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: AppColors.textPrimary,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Expanded(
                        child: SingleChildScrollView(
                          child: Text(
                            _testResults.isEmpty ? 'No tests run yet.' : _testResults,
                            style: TextStyle(
                              color: AppColors.textPrimary,
                              fontFamily: 'monospace',
                              fontSize: 12,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
