// lib/screens/vendor/add_edit_product_screen.dart
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:agrimart/models/product_model.dart';
import 'package:agrimart/services/auth_service.dart'; // Still needed for current user's UID
import 'package:agrimart/services/firestore_service.dart';
import 'package:agrimart/services/local_image_service.dart';
import 'package:agrimart/widgets/product_form.dart';
import 'package:agrimart/models/user_model.dart' as AppUser; // Alias for your UserModel
import 'package:cloud_firestore/cloud_firestore.dart'; // For Timestamp

class AddEditProductScreen extends StatefulWidget {
  final ProductModel? productToEdit; // Null if adding new product
  final AppUser.UserModel currentUserData; // Accepts the populated UserModel

  const AddEditProductScreen({
    super.key,
    this.productToEdit,
    required this.currentUserData, // Make it required
  });

  @override
  State<AddEditProductScreen> createState() => _AddEditProductScreenState();
}

class _AddEditProductScreenState extends State<AddEditProductScreen> {
  final _formKey = GlobalKey<FormState>();
  late TextEditingController _titleController;
  late TextEditingController _descriptionController;
  late TextEditingController _priceController;
  late TextEditingController _categoryController;
  late TextEditingController _quantityController;
  File? _selectedImageFile;
  String? _currentImagePath; // Stores local path from ProductModel or newly saved image

  bool _isLoading = false;
  final FirestoreService _firestoreService = FirestoreService();
  final LocalImageService _localImageService = LocalImageService();
  final AuthService _authService = AuthService(); // Still needed for authService.currentUser!.uid

  @override
  void initState() {
    super.initState();
    // No need to call _loadCurrentUserData() as widget.currentUserData is passed

    _titleController = TextEditingController(text: widget.productToEdit?.title);
    _descriptionController = TextEditingController(text: widget.productToEdit?.description);
    _priceController = TextEditingController(text: widget.productToEdit?.price.toString());
    _categoryController = TextEditingController(text: widget.productToEdit?.category);
    _quantityController = TextEditingController(text: widget.productToEdit?.quantity.toString() ?? '1');
    _currentImagePath = widget.productToEdit?.imageUrl;

    // Verify passed data for debugging if needed
    print("AddEditProductScreen initState: Received currentUserData - Name: ${widget.currentUserData.displayName}, Role: ${widget.currentUserData.role}");
  }

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    _priceController.dispose();
    _categoryController.dispose();
    _quantityController.dispose();
    super.dispose();
  }

  Future<void> _saveProduct() async {
    if (!_formKey.currentState!.validate()) return;

    final firebaseAuthUser = _authService.currentUser;
    if (firebaseAuthUser == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Authentication error. Please log in again.')),
      );
      return;
    }

    if (_selectedImageFile == null && widget.productToEdit == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please select an image for the new product.')),
      );
      return;
    }

    setState(() => _isLoading = true);

    String? imagePathToStore = _currentImagePath;

    try {
      if (_selectedImageFile != null) {
        if (widget.productToEdit?.imageUrl != null && widget.productToEdit!.imageUrl.isNotEmpty) {
          await _localImageService.deleteImageLocally(widget.productToEdit!.imageUrl);
        }
        imagePathToStore = await _localImageService.saveImageLocally(
            _selectedImageFile!,
            firebaseAuthUser.uid, // Use auth UID for organizing local storage path
        );
        if (imagePathToStore == null) throw Exception("Local image saving failed.");
      }

      if (imagePathToStore == null) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Product image is required.')),
          );
          if (mounted) setState(() => _isLoading = false);
          return;
      }

      final now = Timestamp.now();
      final productData = ProductModel(
        id: widget.productToEdit?.id,
        vendorId: firebaseAuthUser.uid, // Crucial: use the auth user's UID
        vendorShopName: widget.currentUserData.displayName ?? "My Shop", // Use passed UserModel's name
        title: _titleController.text.trim(),
        description: _descriptionController.text.trim(),
        price: double.parse(_priceController.text.trim()),
        quantity: int.parse(_quantityController.text.trim()),
        category: _categoryController.text.trim(),
        imageUrl: imagePathToStore,
        createdAt: widget.productToEdit?.createdAt ?? now,
        updatedAt: now,
      );

      if (widget.productToEdit == null) {
        String newProductId = await _firestoreService.addProduct(productData);
        print("Product added with ID: $newProductId. Image at: $imagePathToStore");
      } else {
        await _firestoreService.updateProduct(productData);
        print("Product updated. Image at: $imagePathToStore");
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Product ${widget.productToEdit == null ? "added" : "updated"} successfully!')),
        );
        Navigator.of(context).pop();
      }
    } catch (e) {
      print("Error saving product: $e");
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error saving product: ${e.toString()}')),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.productToEdit == null ? 'Add New Product' : 'Edit Product'),
        actions: [
          if (!_isLoading)
            IconButton(
              icon: const Icon(Icons.save),
              onPressed: _saveProduct,
            ),
          if (_isLoading)
             const Padding(
               padding: EdgeInsets.all(16.0),
               child: SizedBox(width: 20, height: 20, child: CircularProgressIndicator(strokeWidth: 2, color: Colors.white)),
             )
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : ProductForm(
              formKey: _formKey,
              titleController: _titleController,
              descriptionController: _descriptionController,
              priceController: _priceController,
              categoryController: _categoryController,
              quantityController: _quantityController,
              initialImageUrl: _currentImagePath,
              onImagePicked: (File? image) {
                setState(() {
                  _selectedImageFile = image;
                  if (image != null) {
                    // For preview in ProductForm, we can set _currentImagePath, but it's not saved yet.
                    // The actual path to store is determined during _saveProduct after local save.
                    // For simplicity, ProductForm's initialImageUrl will handle showing _selectedImageFile if it's not null
                  }
                });
              },
            ),
    );
  }
}