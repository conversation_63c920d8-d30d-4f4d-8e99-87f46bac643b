import 'package:flutter/material.dart';
import 'package:agrimart/models/order_model.dart';
import 'package:agrimart/models/user_model.dart' as AppUser;
import 'package:agrimart/services/firestore_service.dart';
import 'package:agrimart/main.dart';

class VendorDisputesTab extends StatefulWidget {
  final AppUser.UserModel currentUserData;
  final String vendorId;

  const VendorDisputesTab({
    super.key,
    required this.currentUserData,
    required this.vendorId,
  });

  @override
  State<VendorDisputesTab> createState() => _VendorDisputesTabState();
}

class _VendorDisputesTabState extends State<VendorDisputesTab> {
  final FirestoreService _firestoreService = FirestoreService();

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'open':
        return AppColors.error;
      case 'underreview':
        return Colors.orange;
      case 'resolved':
        return AppColors.success;
      default:
        return AppColors.textSecondary;
    }
  }

  String _getStatusText(String status) {
    switch (status.toLowerCase()) {
      case 'open':
        return 'Open';
      case 'underreview':
        return 'Under Review';
      case 'resolved':
        return 'Resolved';
      default:
        return status;
    }
  }

  void _showDisputeDetails(DisputeModel dispute) async {
    // Get the order details
    final order = await _firestoreService.getOrderById(dispute.orderId);

    if (!mounted) return;

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text('Dispute #${dispute.id?.substring(0, 8) ?? 'Unknown'}'),
            content: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text('Order ID: ${dispute.orderId.substring(0, 8)}'),
                  Text('Customer ID: ${dispute.customerId.substring(0, 8)}'),
                  Text('Status: ${_getStatusText(dispute.status)}'),
                  Text(
                    'Created: ${dispute.createdAt.toDate().toString().split(' ')[0]}',
                  ),
                  const SizedBox(height: 16),
                  const Text(
                    'Reason:',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  Text(dispute.reason),
                  const SizedBox(height: 8),
                  const Text(
                    'Customer Comments:',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  Text(dispute.customerComments),
                  if (dispute.adminNotes != null &&
                      dispute.adminNotes!.isNotEmpty) ...[
                    const SizedBox(height: 8),
                    const Text(
                      'Admin Notes:',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                    Text(dispute.adminNotes!),
                  ],
                  if (order != null) ...[
                    const SizedBox(height: 16),
                    const Text(
                      'Order Details:',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                    Text('Customer: ${order.customerName}'),
                    Text('Total: TZS ${order.totalAmount.toStringAsFixed(0)}'),
                    Text('Address: ${order.shippingAddress}'),
                  ],
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Close'),
              ),
              if (dispute.status.toLowerCase() == 'open')
                TextButton(
                  onPressed: () {
                    Navigator.pop(context);
                    _showResponseDialog(dispute);
                  },
                  child: const Text('Respond'),
                ),
            ],
          ),
    );
  }

  void _showResponseDialog(DisputeModel dispute) {
    final responseController = TextEditingController();

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Respond to Dispute'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Text('Provide your response to this dispute:'),
                const SizedBox(height: 16),
                TextField(
                  controller: responseController,
                  maxLines: 4,
                  decoration: const InputDecoration(
                    hintText: 'Enter your response...',
                    border: OutlineInputBorder(),
                  ),
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Cancel'),
              ),
              TextButton(
                onPressed: () async {
                  if (responseController.text.trim().isNotEmpty) {
                    try {
                      // For now, we'll just show a success message
                      // In a real app, you'd want to add vendor responses to the dispute
                      Navigator.pop(context);
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('Response submitted successfully'),
                          backgroundColor: AppColors.success,
                        ),
                      );
                    } catch (e) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text('Error submitting response: $e'),
                          backgroundColor: AppColors.error,
                        ),
                      );
                    }
                  }
                },
                child: const Text('Submit'),
              ),
            ],
          ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.scaffoldBackground,
      body: StreamBuilder<List<DisputeModel>>(
        stream: _firestoreService.getVendorDisputes(widget.vendorId),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(child: CircularProgressIndicator());
          }
          if (snapshot.hasError) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.error_outline, size: 64, color: AppColors.error),
                  const SizedBox(height: 16),
                  Text(
                    "Error loading disputes",
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: AppColors.textPrimary,
                    ),
                  ),
                ],
              ),
            );
          }
          if (!snapshot.hasData || snapshot.data!.isEmpty) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.check_circle_outline,
                    size: 64,
                    color: AppColors.success,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    "No disputes",
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: AppColors.textPrimary,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    "Great! You have no active disputes",
                    style: TextStyle(color: AppColors.textSecondary),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            );
          }

          final disputes = snapshot.data!;
          return ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: disputes.length,
            itemBuilder: (context, index) {
              final dispute = disputes[index];
              return Card(
                margin: const EdgeInsets.only(bottom: 12),
                elevation: 2,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                child: ListTile(
                  contentPadding: const EdgeInsets.all(16),
                  leading: Icon(
                    Icons.report_problem,
                    color: _getStatusColor(dispute.status),
                    size: 32,
                  ),
                  title: Text(
                    'Dispute #${dispute.id?.substring(0, 8) ?? 'Unknown'}',
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                  subtitle: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const SizedBox(height: 4),
                      Text('Reason: ${dispute.reason}'),
                      Text('Order: ${dispute.orderId.substring(0, 8)}'),
                      Text(
                        'Date: ${dispute.createdAt.toDate().toString().split(' ')[0]}',
                      ),
                    ],
                  ),
                  trailing: Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: _getStatusColor(
                        dispute.status,
                      ).withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      _getStatusText(dispute.status),
                      style: TextStyle(
                        color: _getStatusColor(dispute.status),
                        fontWeight: FontWeight.w600,
                        fontSize: 12,
                      ),
                    ),
                  ),
                  onTap: () => _showDisputeDetails(dispute),
                ),
              );
            },
          );
        },
      ),
    );
  }
}
