import 'package:flutter/material.dart';
import 'package:agrimart/models/order_model.dart';
import 'package:agrimart/models/user_model.dart' as AppUser;
import 'package:agrimart/services/firestore_service.dart';
import 'package:agrimart/main.dart';
import 'package:intl/intl.dart';
import 'dart:io';

class VendorOrdersTab extends StatefulWidget {
  final AppUser.UserModel currentUserData;
  final String vendorId;

  const VendorOrdersTab({
    super.key,
    required this.currentUserData,
    required this.vendorId,
  });

  @override
  State<VendorOrdersTab> createState() => _VendorOrdersTabState();
}

class _VendorOrdersTabState extends State<VendorOrdersTab> {
  final FirestoreService _firestoreService = FirestoreService();

  Color _getStatusColor(OrderStatus status) {
    switch (status) {
      case OrderStatus.pendingConfirmation:
        return Colors.orange;
      case OrderStatus.shipped:
        return Colors.blue;
      case OrderStatus.delivered:
        return AppColors.success;
      case OrderStatus.disputed:
        return AppColors.error;
      case OrderStatus.completed:
        return AppColors.primaryGreen;
      case OrderStatus.cancelledByCustomer:
      case OrderStatus.cancelledByVendor:
        return Colors.grey;
      default:
        return AppColors.textSecondary;
    }
  }

  String _getStatusText(OrderStatus status) {
    switch (status) {
      case OrderStatus.pendingConfirmation:
        return 'Pending';
      case OrderStatus.shipped:
        return 'Shipped';
      case OrderStatus.delivered:
        return 'Delivered';
      case OrderStatus.disputed:
        return 'Disputed';
      case OrderStatus.completed:
        return 'Completed';
      case OrderStatus.cancelledByCustomer:
        return 'Cancelled by Customer';
      case OrderStatus.cancelledByVendor:
        return 'Cancelled by Vendor';
      default:
        return status.toString().split('.').last;
    }
  }

  Future<void> _updateOrderStatus(String orderId, OrderStatus newStatus) async {
    try {
      await _firestoreService.updateOrderStatusAndDetails(orderId, newStatus);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Order status updated to ${_getStatusText(newStatus)}',
            ),
            backgroundColor: AppColors.success,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error updating order status: $e'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  void _showOrderDetails(OrderModel order) {
    final vendorItems =
        order.items.where((item) => item.vendorId == widget.vendorId).toList();
    final vendorTotal = vendorItems.fold<double>(
      0,
      (sum, item) => sum + (item.priceAtPurchase * item.quantity),
    );

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text('Order #${order.id?.substring(0, 8) ?? 'Unknown'}'),
            content: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text('Customer: ${order.customerName}'),
                  Text('Email: ${order.customerEmail}'),
                  Text('Address: ${order.shippingAddress}'),
                  Text('Status: ${_getStatusText(order.orderStatus)}'),
                  Text(
                    'Transaction: ${_getTransactionStatusText(_getEffectiveTransactionStatus(order))}',
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Vendor Shipped: ${order.vendorShipmentConfirmed ? "✅ Yes" : "❌ No"}',
                  ),
                  Text(
                    'Customer Confirmed: ${order.customerDeliveryConfirmed ? "✅ Yes" : "❌ No"}',
                  ),
                  const SizedBox(height: 16),
                  const Text(
                    'Your Items:',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  ...vendorItems.map(
                    (item) => Padding(
                      padding: const EdgeInsets.symmetric(vertical: 4),
                      child: Text(
                        '${item.productName} x${item.quantity} - TZS ${(item.priceAtPurchase * item.quantity).toStringAsFixed(0)}',
                      ),
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Your Total: TZS ${vendorTotal.toStringAsFixed(0)}',
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Close'),
              ),
              if (order.orderStatus == OrderStatus.pendingConfirmation &&
                  !order.vendorShipmentConfirmed)
                TextButton(
                  onPressed: () {
                    Navigator.pop(context);
                    _confirmShipment(order.id!);
                  },
                  child: const Text('Confirm Shipment'),
                ),
            ],
          ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.scaffoldBackground,
      body: StreamBuilder<List<OrderModel>>(
        stream: _firestoreService.getVendorOrdersOptimized(widget.vendorId),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(child: CircularProgressIndicator());
          }
          if (snapshot.hasError) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.error_outline, size: 64, color: AppColors.error),
                  const SizedBox(height: 16),
                  Text(
                    "Error loading orders",
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: AppColors.textPrimary,
                    ),
                  ),
                ],
              ),
            );
          }
          if (!snapshot.hasData || snapshot.data!.isEmpty) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.shopping_cart_outlined,
                    size: 64,
                    color: AppColors.textSecondary,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    "No orders yet",
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: AppColors.textPrimary,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    "Orders containing your products will appear here",
                    style: TextStyle(color: AppColors.textSecondary),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            );
          }

          final orders = snapshot.data!;
          return ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: orders.length,
            itemBuilder: (context, index) {
              final order = orders[index];
              return _buildOrderCard(context, order);
            },
          );
        },
      ),
    );
  }

  String _getTransactionStatusText(TransactionStatus status) {
    switch (status) {
      case TransactionStatus.held:
        return 'Held in Escrow';
      case TransactionStatus.released:
        return 'Released to Vendor';
      case TransactionStatus.refunded:
        return 'Refunded to Customer';
      case TransactionStatus.disputed:
        return 'Held (Disputed)';
    }
  }

  // Helper method to get effective transaction status for existing orders
  TransactionStatus _getEffectiveTransactionStatus(OrderModel order) {
    return FirestoreService.getTransactionStatusFromOrderStatus(
      order.orderStatus,
      order.transactionStatus,
    );
  }

  Future<void> _confirmShipment(String orderId) async {
    try {
      await _firestoreService.confirmVendorShipment(orderId, widget.vendorId);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text(
              'Shipment confirmed! Customer will be notified.',
            ),
            backgroundColor: AppColors.success,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error confirming shipment: $e'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  Widget _buildOrderCard(BuildContext context, OrderModel order) {
    final vendorItems =
        order.items.where((item) => item.vendorId == widget.vendorId).toList();
    final vendorTotal = vendorItems.fold<double>(
      0,
      (sum, item) => sum + (item.priceAtPurchase * item.quantity),
    );

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: AppColors.cardBackground,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: AppColors.primaryGreen.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(16),
          onTap: () => _showOrderDetails(order),
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header Row
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: _getStatusColor(
                          order.orderStatus,
                        ).withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Icon(
                        _getStatusIcon(order.orderStatus),
                        color: _getStatusColor(order.orderStatus),
                        size: 20,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            "Order #${order.id?.substring(0, 8) ?? 'Unknown'}",
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: AppColors.textPrimary,
                            ),
                          ),
                          const SizedBox(height: 2),
                          Text(
                            DateFormat.yMMMd().add_jm().format(
                              order.createdAt.toDate(),
                            ),
                            style: TextStyle(
                              fontSize: 12,
                              color: AppColors.textSecondary,
                            ),
                          ),
                        ],
                      ),
                    ),
                    Icon(Icons.chevron_right, color: AppColors.textSecondary),
                  ],
                ),

                const SizedBox(height: 16),

                // Status and Transaction Info
                Row(
                  children: [
                    Expanded(
                      child: _buildInfoChip(
                        "Status",
                        _getStatusText(order.orderStatus),
                        _getStatusColor(order.orderStatus),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: _buildInfoChip(
                        "Transaction",
                        _getTransactionStatusText(
                          _getEffectiveTransactionStatus(order),
                        ),
                        _getTransactionStatusColor(
                          _getEffectiveTransactionStatus(order),
                        ),
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 16),

                // Customer and Order Info
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: AppColors.surfaceLight,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Column(
                    children: [
                      _buildInfoRow("Customer", order.customerName),
                      const SizedBox(height: 8),
                      _buildInfoRow("Email", order.customerEmail),
                      const SizedBox(height: 8),
                      _buildInfoRow(
                        "Your Items",
                        "${vendorItems.length} item${vendorItems.length > 1 ? 's' : ''}",
                      ),
                      const SizedBox(height: 8),
                      _buildInfoRow(
                        "Your Total",
                        "TZS ${vendorTotal.toStringAsFixed(0)}",
                      ),
                    ],
                  ),
                ),

                // Confirmation Status (if applicable)
                if (order.orderStatus == OrderStatus.shipped ||
                    order.orderStatus == OrderStatus.delivered ||
                    order.orderStatus == OrderStatus.completed) ...[
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      Expanded(
                        child: _buildConfirmationStatus(
                          "You Shipped",
                          order.vendorShipmentConfirmed,
                          order.vendorConfirmedAt,
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: _buildConfirmationStatus(
                          "Customer Confirmed",
                          order.customerDeliveryConfirmed,
                          order.customerConfirmedAt,
                        ),
                      ),
                    ],
                  ),
                ],

                // Action Button
                if (order.orderStatus == OrderStatus.pendingConfirmation &&
                    !order.vendorShipmentConfirmed) ...[
                  const SizedBox(height: 16),
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton.icon(
                      icon: const Icon(Icons.local_shipping),
                      label: const Text("Confirm Shipment"),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.primaryGreen,
                        foregroundColor: AppColors.textOnPrimary,
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      onPressed: () => _confirmShipment(order.id!),
                    ),
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildInfoChip(String label, String value, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: 10,
              color: AppColors.textSecondary,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 2),
          Text(
            value,
            style: TextStyle(
              fontSize: 12,
              color: color,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 80,
          child: Text(
            label,
            style: TextStyle(
              fontSize: 14,
              color: AppColors.textSecondary,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Text(
            value,
            style: TextStyle(
              fontSize: 14,
              color: AppColors.textPrimary,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildConfirmationStatus(
    String label,
    bool confirmed,
    dynamic timestamp,
  ) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color:
            confirmed
                ? AppColors.success.withValues(alpha: 0.1)
                : AppColors.textSecondary.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color:
              confirmed
                  ? AppColors.success.withValues(alpha: 0.3)
                  : AppColors.textSecondary.withValues(alpha: 0.3),
        ),
      ),
      child: Column(
        children: [
          Icon(
            confirmed ? Icons.check_circle : Icons.radio_button_unchecked,
            size: 20,
            color: confirmed ? AppColors.success : AppColors.textSecondary,
          ),
          const SizedBox(height: 4),
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: confirmed ? AppColors.success : AppColors.textSecondary,
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
          if (confirmed && timestamp != null) ...[
            const SizedBox(height: 2),
            Text(
              DateFormat.MMMd().add_jm().format(timestamp.toDate()),
              style: TextStyle(fontSize: 10, color: AppColors.textSecondary),
              textAlign: TextAlign.center,
            ),
          ],
        ],
      ),
    );
  }

  IconData _getStatusIcon(OrderStatus status) {
    switch (status) {
      case OrderStatus.pendingConfirmation:
        return Icons.hourglass_empty;
      case OrderStatus.shipped:
        return Icons.local_shipping;
      case OrderStatus.delivered:
        return Icons.check_circle;
      case OrderStatus.completed:
        return Icons.verified;
      case OrderStatus.disputed:
        return Icons.report_problem;
      case OrderStatus.cancelledByCustomer:
      case OrderStatus.cancelledByVendor:
        return Icons.cancel;
      default:
        return Icons.info;
    }
  }

  Color _getTransactionStatusColor(TransactionStatus status) {
    switch (status) {
      case TransactionStatus.held:
        return Colors.orange;
      case TransactionStatus.released:
        return AppColors.success;
      case TransactionStatus.refunded:
        return Colors.blue;
      case TransactionStatus.disputed:
        return AppColors.error;
    }
  }
}
