import 'package:flutter/material.dart';
import 'package:agrimart/models/vendor_analytics_model.dart';
import 'package:agrimart/models/user_model.dart' as AppUser;
import 'package:agrimart/services/firestore_service.dart';
import 'package:agrimart/main.dart';

class VendorAnalyticsTab extends StatefulWidget {
  final AppUser.UserModel currentUserData;
  final String vendorId;

  const VendorAnalyticsTab({
    super.key,
    required this.currentUserData,
    required this.vendorId,
  });

  @override
  State<VendorAnalyticsTab> createState() => _VendorAnalyticsTabState();
}

class _VendorAnalyticsTabState extends State<VendorAnalyticsTab> {
  final FirestoreService _firestoreService = FirestoreService();

  Widget _buildStatCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Icon(icon, color: color, size: 32),
            const SizedBox(height: 8),
            Text(
              value,
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: AppColors.textPrimary,
              ),
            ),
            Text(
              title,
              style: TextStyle(
                color: AppColors.textSecondary,
                fontSize: 12,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTopProductsCard(List<MapEntry<String, int>> topProducts) {
    return Card(
      margin: const EdgeInsets.all(16),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Top Selling Products',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: AppColors.textPrimary,
              ),
            ),
            const SizedBox(height: 16),
            if (topProducts.isEmpty)
              Center(
                child: Text(
                  'No sales data available',
                  style: TextStyle(color: AppColors.textSecondary),
                ),
              )
            else
              ...topProducts.asMap().entries.map((entry) {
                final index = entry.key;
                final product = entry.value;
                return Padding(
                  padding: const EdgeInsets.symmetric(vertical: 4),
                  child: Row(
                    children: [
                      Container(
                        width: 24,
                        height: 24,
                        decoration: BoxDecoration(
                          color: AppColors.primaryGreen,
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Center(
                          child: Text(
                            '${index + 1}',
                            style: TextStyle(
                              color: AppColors.textOnPrimary,
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          product.key,
                          style: TextStyle(
                            color: AppColors.textPrimary,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                      Text(
                        '${product.value} sold',
                        style: TextStyle(
                          color: AppColors.textSecondary,
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                );
              }),
          ],
        ),
      ),
    );
  }

  Widget _buildMonthlySalesCard(List<MapEntry<String, double>> monthlySales) {
    return Card(
      margin: const EdgeInsets.all(16),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Monthly Sales',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: AppColors.textPrimary,
              ),
            ),
            const SizedBox(height: 16),
            if (monthlySales.isEmpty)
              Center(
                child: Text(
                  'No sales data available',
                  style: TextStyle(color: AppColors.textSecondary),
                ),
              )
            else
              ...monthlySales.map((entry) {
                return Padding(
                  padding: const EdgeInsets.symmetric(vertical: 4),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        entry.key,
                        style: TextStyle(
                          color: AppColors.textPrimary,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      Text(
                        'TZS ${entry.value.toStringAsFixed(0)}',
                        style: TextStyle(
                          color: AppColors.primaryGreen,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                );
              }),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.scaffoldBackground,
      body: FutureBuilder<VendorSalesAnalytics>(
        future: _firestoreService.getVendorSalesAnalytics(widget.vendorId),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(child: CircularProgressIndicator());
          }
          
          final analytics = snapshot.data ?? VendorSalesAnalytics.empty(widget.vendorId);
          
          return SingleChildScrollView(
            child: Column(
              children: [
                const SizedBox(height: 16),
                
                // Stats Grid
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: GridView.count(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    crossAxisCount: 2,
                    crossAxisSpacing: 16,
                    mainAxisSpacing: 16,
                    children: [
                      _buildStatCard(
                        title: 'Total Revenue',
                        value: 'TZS ${analytics.totalRevenue.toStringAsFixed(0)}',
                        icon: Icons.attach_money,
                        color: AppColors.primaryGreen,
                      ),
                      _buildStatCard(
                        title: 'Total Orders',
                        value: analytics.totalOrders.toString(),
                        icon: Icons.shopping_cart,
                        color: Colors.blue,
                      ),
                      _buildStatCard(
                        title: 'Average Order',
                        value: 'TZS ${analytics.averageOrderValue.toStringAsFixed(0)}',
                        icon: Icons.trending_up,
                        color: Colors.orange,
                      ),
                      _buildStatCard(
                        title: 'Products Sold',
                        value: analytics.productSales.values.fold<int>(0, (sum, qty) => sum + qty).toString(),
                        icon: Icons.inventory,
                        color: Colors.purple,
                      ),
                    ],
                  ),
                ),
                
                // Top Products
                _buildTopProductsCard(analytics.topSellingProducts),
                
                // Monthly Sales
                _buildMonthlySalesCard(analytics.monthlySalesData),
                
                const SizedBox(height: 16),
              ],
            ),
          );
        },
      ),
    );
  }
}
