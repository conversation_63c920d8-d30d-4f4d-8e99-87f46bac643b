import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:agrimart/models/user_model.dart' as AppUser;
import 'package:agrimart/services/auth_service.dart';

import 'package:agrimart/widgets/user_profile_avatar.dart';
import 'package:agrimart/screens/profile/profile_screen.dart';
import 'package:agrimart/screens/vendor/vendor_products_tab.dart';
import 'package:agrimart/screens/vendor/vendor_orders_tab.dart';
import 'package:agrimart/screens/vendor/vendor_reviews_tab.dart';
import 'package:agrimart/screens/vendor/vendor_analytics_tab.dart';
import 'package:agrimart/screens/vendor/vendor_disputes_tab.dart';
import 'package:agrimart/main.dart';

class EnhancedVendorDashboardScreen extends StatefulWidget {
  final AppUser.UserModel currentUserData;

  const EnhancedVendorDashboardScreen({
    super.key,
    required this.currentUserData,
  });

  @override
  State<EnhancedVendorDashboardScreen> createState() =>
      _EnhancedVendorDashboardScreenState();
}

class _EnhancedVendorDashboardScreenState
    extends State<EnhancedVendorDashboardScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 5, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final AuthService authService = Provider.of<AuthService>(
      context,
      listen: false,
    );
    final firebaseAuthUser = authService.currentUser;

    if (firebaseAuthUser == null) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          Navigator.of(context).popUntil((route) => route.isFirst);
        }
      });
      return const Scaffold(
        body: Center(child: Text("Authentication error. Please log in again.")),
      );
    }

    String shopName = widget.currentUserData.displayName ?? "My Shop";

    return Scaffold(
      backgroundColor: AppColors.scaffoldBackground,
      appBar: AppBar(
        title: Text("$shopName - Dashboard"),
        backgroundColor: AppColors.primaryGreen,
        foregroundColor: AppColors.textOnPrimary,
        elevation: 2,
        actions: [
          Padding(
            padding: const EdgeInsets.only(right: 16.0),
            child: UserProfileAvatar(
              user: widget.currentUserData,
              size: 36,
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (_) => ProfileScreen(user: widget.currentUserData),
                  ),
                );
              },
            ),
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          isScrollable: true,
          indicatorColor: AppColors.textOnPrimary,
          labelColor: AppColors.textOnPrimary,
          unselectedLabelColor: AppColors.textOnPrimary.withValues(alpha: 0.7),
          tabs: const [
            Tab(icon: Icon(Icons.inventory), text: 'Products'),
            Tab(icon: Icon(Icons.shopping_cart), text: 'Orders'),
            Tab(icon: Icon(Icons.star), text: 'Reviews'),
            Tab(icon: Icon(Icons.analytics), text: 'Analytics'),
            Tab(icon: Icon(Icons.report_problem), text: 'Disputes'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          VendorProductsTab(
            currentUserData: widget.currentUserData,
            vendorId: firebaseAuthUser.uid,
          ),
          VendorOrdersTab(
            currentUserData: widget.currentUserData,
            vendorId: firebaseAuthUser.uid,
          ),
          VendorReviewsTab(
            currentUserData: widget.currentUserData,
            vendorId: firebaseAuthUser.uid,
          ),
          VendorAnalyticsTab(
            currentUserData: widget.currentUserData,
            vendorId: firebaseAuthUser.uid,
          ),
          VendorDisputesTab(
            currentUserData: widget.currentUserData,
            vendorId: firebaseAuthUser.uid,
          ),
        ],
      ),
    );
  }
}
