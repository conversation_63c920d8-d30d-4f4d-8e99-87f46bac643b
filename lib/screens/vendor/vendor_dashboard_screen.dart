// lib/screens/vendor/vendor_dashboard_screen.dart
import 'dart:io'; // For File type if displaying local images
import 'package:agrimart/screens/vendor/add_edit_product_screen.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:agrimart/models/product_model.dart';
import 'package:agrimart/services/auth_service.dart';
import 'package:agrimart/services/firestore_service.dart';
import 'package:agrimart/services/local_image_service.dart';
import 'package:agrimart/models/user_model.dart'
    as AppUser; // Alias for your UserModel
import 'package:agrimart/widgets/user_profile_avatar.dart';
// import 'package:agrimart/widgets/modern_app_bar.dart';
import 'package:agrimart/screens/profile/profile_screen.dart';
import 'package:agrimart/main.dart';

class VendorDashboardScreen extends StatefulWidget {
  final AppUser.UserModel currentUserData; // Accepts the populated UserModel

  const VendorDashboardScreen({super.key, required this.currentUserData});

  @override
  State<VendorDashboardScreen> createState() => _VendorDashboardScreenState();
}

class _VendorDashboardScreenState extends State<VendorDashboardScreen> {
  // final AuthService _authService = AuthService();
  final FirestoreService _firestoreService = FirestoreService();
  final LocalImageService _localImageService = LocalImageService();

  Future<void> _deleteProduct(
    BuildContext context,
    ProductModel product,
  ) async {
    final confirm = await showDialog<bool>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Confirm Delete'),
          content: Text(
            'Are you sure you want to delete "${product.title}"? This action cannot be undone.',
          ),
          actions: <Widget>[
            TextButton(
              child: const Text('Cancel'),
              onPressed: () => Navigator.of(context).pop(false),
            ),
            TextButton(
              style: TextButton.styleFrom(foregroundColor: Colors.red),
              child: const Text('Delete'),
              onPressed: () => Navigator.of(context).pop(true),
            ),
          ],
        );
      },
    );

    if (confirm == true) {
      try {
        if (product.imageUrl.isNotEmpty) {
          await _localImageService.deleteImageLocally(product.imageUrl);
        }
        await _firestoreService.deleteProduct(product.id!);
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('${product.title} deleted successfully')),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(SnackBar(content: Text('Error deleting product: $e')));
        }
      }
    }
  }

  Widget _buildProductImage(String imagePath) {
    File imageFile = File(imagePath);
    if (imagePath.isNotEmpty && imageFile.existsSync()) {
      return SizedBox(
        width: 60,
        height: 60,
        child: Image.file(
          imageFile,
          fit: BoxFit.cover,
          errorBuilder:
              (context, error, stackTrace) =>
                  const Icon(Icons.broken_image, size: 40),
        ),
      );
    } else {
      return const SizedBox(
        width: 60,
        height: 60,
        child: Icon(Icons.image_not_supported, size: 40, color: Colors.grey),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    // Get the Firebase Auth user for UID, as this is the primary key for vendorId
    final AuthService authService = Provider.of<AuthService>(
      context,
      listen: false,
    ); // Get from Provider
    final firebaseAuthUser =
        authService.currentUser; // Use the provided instance

    if (firebaseAuthUser == null) {
      // This should ideally not happen if routing from HomeScreen is correct
      // and user is authenticated.
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          // Navigate back or to login if auth user is lost
          Navigator.of(context).popUntil((route) => route.isFirst);
        }
      });
      return const Scaffold(
        body: Center(child: Text("Authentication error. Please log in again.")),
      );
    }

    // Use widget.currentUserData for display names, shop names etc.
    // Use firebaseAuthUser.uid for queries related to 'vendorId' in Firestore
    String shopName = widget.currentUserData.displayName ?? "My Shop";

    return Scaffold(
      backgroundColor: AppColors.scaffoldBackground,
      appBar: AppBar(
        title: Text("$shopName - Dashboard"),
        backgroundColor: AppColors.primaryGreen,
        foregroundColor: AppColors.textOnPrimary,
        elevation: 2,
        actions: [
          Padding(
            padding: const EdgeInsets.only(right: 16.0),
            child: UserProfileAvatar(
              user: widget.currentUserData,
              size: 36,
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (_) => ProfileScreen(user: widget.currentUserData),
                  ),
                );
              },
            ),
          ),
        ],
      ),
      body: StreamBuilder<List<ProductModel>>(
        stream: _firestoreService.getVendorProducts(
          firebaseAuthUser.uid,
        ), // Query by auth UID
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(child: CircularProgressIndicator());
          }
          if (snapshot.hasError) {
            // This is where the index error would have shown up
            print("Error fetching vendor products: ${snapshot.error}");
            return Center(
              child: Text(
                "Error loading products. If this persists, ensure Firestore indexes are set up. Details: ${snapshot.error}",
              ),
            );
          }
          if (!snapshot.hasData || snapshot.data!.isEmpty) {
            return const Center(
              child: Padding(
                padding: EdgeInsets.all(16.0),
                child: Text(
                  "You haven't added any products yet. Tap the '+' button to add your first product!",
                  textAlign: TextAlign.center,
                  style: TextStyle(fontSize: 16),
                ),
              ),
            );
          }

          final products = snapshot.data!;
          return ListView.builder(
            itemCount: products.length,
            itemBuilder: (context, index) {
              final product = products[index];
              return Card(
                margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                child: ListTile(
                  leading: _buildProductImage(product.imageUrl),
                  title: Text(
                    product.title,
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                  subtitle: Text(
                    "TZS ${product.price.toStringAsFixed(0)}\nCategory: ${product.category}\nQty: ${product.quantity}",
                  ),
                  isThreeLine: true,
                  trailing: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      IconButton(
                        icon: Icon(
                          Icons.edit,
                          color: Theme.of(context).primaryColor,
                        ),
                        onPressed: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder:
                                  (_) => AddEditProductScreen(
                                    productToEdit: product,
                                    currentUserData:
                                        widget
                                            .currentUserData, // Pass the UserModel
                                  ),
                            ),
                          );
                        },
                        tooltip: "Edit Product",
                      ),
                      IconButton(
                        icon: Icon(Icons.delete, color: Colors.red[700]),
                        onPressed: () => _deleteProduct(context, product),
                        tooltip: "Delete Product",
                      ),
                    ],
                  ),
                ),
              );
            },
          );
        },
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder:
                  (_) => AddEditProductScreen(
                    currentUserData:
                        widget.currentUserData, // Pass the UserModel
                  ),
            ),
          );
        },
        tooltip: 'Add Product',
        child: const Icon(Icons.add),
      ),
    );
  }
}
