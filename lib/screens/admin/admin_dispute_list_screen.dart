// lib/screens/admin/admin_dispute_list_screen.dart (Illustrative)
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:agrimart/services/firestore_service.dart';
import 'package:agrimart/models/order_model.dart'; // For DisputeModel
import 'package:intl/intl.dart';

class AdminDisputeListScreen extends StatelessWidget {
  const AdminDisputeListScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final firestoreService = Provider.of<FirestoreService>(context, listen: false);

    return Scaffold(
      appBar: AppBar(title: const Text("Manage Disputes")),
      body: StreamBuilder<List<DisputeModel>>(
        stream: firestoreService.getAllDisputes(),
        builder: (context, snapshot) {
          // ... (Handle loading, error, empty states) ...
          if (!snapshot.hasData || snapshot.data!.isEmpty) {
            return const Center(child: Text("No open disputes."));
          }
          final disputes = snapshot.data!;
          return ListView.builder(
            itemCount: disputes.length,
            itemBuilder: (context, index) {
              final dispute = disputes[index];
              return ListTile(
                title: Text("Dispute ID: ...${dispute.id?.substring(dispute.id!.length - 6)} (Order: ...${dispute.orderId.substring(dispute.orderId.length-6)})"),
                subtitle: Text("Reason: ${dispute.reason}\nStatus: ${dispute.status}\nRaised: ${DateFormat.yMMMd().format(dispute.createdAt.toDate())}"),
                isThreeLine: true,
                trailing: dispute.status.toLowerCase() == "open" || dispute.status.toLowerCase() == "underreview"
                  ? ElevatedButton(
                      child: const Text("Resolve"),
                      onPressed: () async {
                        // Show a dialog for admin to enter resolution notes
                        // Then update dispute status to "Resolved"
                        // And update corresponding Order status to "resolvedDispute" or "completed"
                        // Example:
                        // await firestoreService.updateDisputeStatus(dispute.id!, "Resolved", adminNotes: "Admin notes here");
                        // await firestoreService.updateOrderStatusAndDetails(dispute.orderId, OrderStatus.resolvedDispute, adminResolutionNotes: "Admin notes here");
                        ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text("Resolve action TBD")));
                      },
                    )
                  : Text(dispute.status, style: const TextStyle(fontWeight: FontWeight.bold)),
                 onTap: (){
                   // Navigate to a dispute detail screen for admin
                 },
              );
            },
          );
        },
      ),
    );
  }
}