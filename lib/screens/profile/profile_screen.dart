import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../models/user_model.dart';
import '../../services/auth_service.dart';
import '../../services/firestore_service.dart';
import '../../widgets/user_profile_avatar.dart';
import '../../widgets/modern_app_bar.dart';
import '../../main.dart';
import '../auth/login_screen.dart';
import 'edit_profile_screen.dart';
import '../customer/customer_reviews_screen.dart';

class ProfileScreen extends StatefulWidget {
  final UserModel user;

  const ProfileScreen({super.key, required this.user});

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen> {
  final FirestoreService _firestoreService = FirestoreService();
  int _itemCount = 0;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadItemCount();
  }

  Future<void> _loadItemCount() async {
    try {
      if (widget.user.role == UserRole.vendor) {
        final count = await _firestoreService.getVendorProductCount(
          widget.user.uid,
        );
        setState(() {
          _itemCount = count;
          _isLoading = false;
        });
      } else {
        // For customers, load actual order count
        final count = await _firestoreService.getCustomerOrderCount(
          widget.user.uid,
        );
        setState(() {
          _itemCount = count;
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _itemCount = 0;
        _isLoading = false;
      });
    }
  }

  Widget _buildProfileOption({
    required IconData icon,
    required String title,
    String? subtitle,
    VoidCallback? onTap,
    Color? iconColor,
    Widget? trailing,
  }) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      child: ListTile(
        leading: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: (iconColor ?? AppColors.primaryGreen).withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            icon,
            color: iconColor ?? AppColors.primaryGreen,
            size: 24,
          ),
        ),
        title: Text(
          title,
          style: const TextStyle(
            fontWeight: FontWeight.w600,
            color: AppColors.textPrimary,
          ),
        ),
        subtitle:
            subtitle != null
                ? Text(
                  subtitle,
                  style: const TextStyle(color: AppColors.textSecondary),
                )
                : null,
        trailing:
            trailing ??
            const Icon(Icons.chevron_right, color: AppColors.textTertiary),
        onTap: onTap,
      ),
    );
  }

  Widget _buildStatsCard() {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppColors.cardBackground,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: AppColors.primaryGreen.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Account Statistics',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppColors.textPrimary,
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildStatItem(
                  icon: Icons.calendar_today,
                  label: 'Member Since',
                  value: _formatDate(widget.user.createdAt.toDate()),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildStatItem(
                  icon:
                      widget.user.role == UserRole.vendor
                          ? Icons.store
                          : Icons.shopping_bag,
                  label:
                      widget.user.role == UserRole.vendor
                          ? 'Products'
                          : 'Orders',
                  value: _isLoading ? '...' : _itemCount.toString(),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem({
    required IconData icon,
    required String label,
    required String value,
  }) {
    return Column(
      children: [
        Icon(icon, color: AppColors.primaryGreen, size: 32),
        const SizedBox(height: 8),
        Text(
          value,
          style: const TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: AppColors.textPrimary,
          ),
        ),
        Text(
          label,
          style: const TextStyle(fontSize: 12, color: AppColors.textSecondary),
        ),
      ],
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.scaffoldBackground,
      appBar: const ModernAppBar(title: 'Profile', centerTitle: true),
      body: SingleChildScrollView(
        child: Column(
          children: [
            // Profile Header
            ProfileHeader(
              user: widget.user,
              onEditPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (_) => EditProfileScreen(user: widget.user),
                  ),
                );
              },
            ),

            const SizedBox(height: 20),

            // Stats Card
            _buildStatsCard(),

            const SizedBox(height: 8),

            // Profile Options
            _buildProfileOption(
              icon: Icons.person,
              title: 'Edit Profile',
              subtitle: 'Update your personal information',
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (_) => EditProfileScreen(user: widget.user),
                  ),
                );
              },
            ),

            if (widget.user.role == UserRole.customer)
              _buildProfileOption(
                icon: Icons.star,
                title: 'My Reviews',
                subtitle: 'View and manage your vendor reviews',
                onTap: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder:
                          (_) =>
                              CustomerReviewsScreen(currentUser: widget.user),
                    ),
                  );
                },
              ),

            // _buildProfileOption(
            //   icon: Icons.history,
            //   title:
            //       user.role == UserRole.vendor
            //           ? 'Order History'
            //           : 'Purchase History',
            //   subtitle:
            //       'View your ${user.role == UserRole.vendor ? 'sales' : 'orders'} history',
            //   onTap: () {
            //     // TODO: Navigate to order history
            //     ScaffoldMessenger.of(context).showSnackBar(
            //       const SnackBar(
            //         content: Text('Order history feature coming soon!'),
            //       ),
            //     );
            //   },
            // ),

            // _buildProfileOption(
            //   icon: Icons.notifications,
            //   title: 'Notifications',
            //   subtitle: 'Manage your notification preferences',
            //   onTap: () {
            //     // TODO: Navigate to notifications settings
            //     ScaffoldMessenger.of(context).showSnackBar(
            //       const SnackBar(
            //         content: Text('Notification settings coming soon!'),
            //       ),
            //     );
            //   },
            // ),

            // _buildProfileOption(
            //   icon: Icons.help,
            //   title: 'Help & Support',
            //   subtitle: 'Get help and contact support',
            //   onTap: () {
            //     // TODO: Navigate to help screen
            //     ScaffoldMessenger.of(context).showSnackBar(
            //       const SnackBar(content: Text('Help & Support coming soon!')),
            //     );
            //   },
            // ),
            _buildProfileOption(
              icon: Icons.info,
              title: 'About',
              subtitle: 'App version and information',
              onTap: () {
                showAboutDialog(
                  context: context,
                  applicationName: 'Agrimart',
                  applicationVersion: '1.0.0',
                  applicationIcon: Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: AppColors.primaryGreen,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Icon(
                      Icons.eco,
                      color: AppColors.textOnPrimary,
                      size: 32,
                    ),
                  ),
                  children: [
                    const Text(
                      'Agricultural marketplace connecting farmers with customers.',
                    ),
                  ],
                );
              },
            ),

            const SizedBox(height: 16),

            // Logout Button
            Container(
              margin: const EdgeInsets.symmetric(horizontal: 16),
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: () async {
                  print("ProfileScreen: Logout button pressed");
                  try {
                    final authService = Provider.of<AuthService>(
                      context,
                      listen: false,
                    );
                    print("ProfileScreen: Calling authService.signOut()");
                    await authService.signOut();
                    print("ProfileScreen: authService.signOut() completed");

                    // For MIUI devices, add extra delay and manual navigation
                    await Future.delayed(const Duration(milliseconds: 500));

                    // Check if user is actually logged out
                    final currentUser = authService.currentUser;
                    print(
                      "ProfileScreen: Current user after logout: $currentUser",
                    );

                    if (currentUser == null) {
                      print(
                        "ProfileScreen: Logout successful, navigating to login",
                      );
                      // Force navigation to login screen for MIUI devices
                      if (context.mounted) {
                        Navigator.of(context).pushAndRemoveUntil(
                          MaterialPageRoute(
                            builder: (context) => const LoginScreen(),
                          ),
                          (route) => false,
                        );
                      }
                    } else {
                      print(
                        "ProfileScreen: WARNING - User still logged in after signOut",
                      );
                      throw Exception(
                        "Logout failed - user still authenticated",
                      );
                    }

                    // Show a brief success message for MIUI devices
                    if (context.mounted) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('Signed out successfully'),
                          duration: Duration(seconds: 1),
                        ),
                      );
                    }
                  } catch (e) {
                    print("ProfileScreen: Error during logout: $e");
                    if (context.mounted) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text('Logout failed: $e'),
                          backgroundColor: Colors.red,
                        ),
                      );
                    }
                  }
                },
                icon: const Icon(Icons.logout),
                label: const Text('Sign Out'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.error,
                  foregroundColor: AppColors.textOnPrimary,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
              ),
            ),

            const SizedBox(height: 32),
          ],
        ),
      ),
    );
  }
}
