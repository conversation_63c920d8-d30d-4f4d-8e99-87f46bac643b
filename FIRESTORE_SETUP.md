# Firestore Setup Guide

## 🔒 Security Rules

Go to Firebase Console → Firestore Database → Rules and apply these rules:

```javascript
rules_version = '2';

service cloud.firestore {
  match /databases/{database}/documents {

    // Helper function to get user role from their user document
    function getUserRole(userId) {
      return get(/databases/$(database)/documents/users/$(userId)).data.role;
    }

    // Helper function to check if user is admin
    function isAdmin() {
      return request.auth != null && getUserRole(request.auth.uid) == 'admin';
    }

    // Helper function to check if user is vendor
    function isVendor() {
      return request.auth != null && getUserRole(request.auth.uid) == 'vendor';
    }

    // Helper function to check if user is customer
    function isCustomer() {
      return request.auth != null && getUserRole(request.auth.uid) == 'customer';
    }

    // --- Users Collection ---
    match /users/{userId} {
      allow create: if request.auth != null && request.auth.uid == userId;
      allow read, update: if request.auth != null && request.auth.uid == userId;
      allow read: if isAdmin();
      allow delete: if false;
    }

    // --- Products Collection ---
    match /products/{productId} {
      allow read: if true;
      allow create: if request.auth != null &&
                       request.resource.data.vendorId == request.auth.uid &&
                       isVendor();
      allow update, delete: if request.auth != null &&
                               resource.data.vendorId == request.auth.uid &&
                               isVendor();
      allow update, delete: if isAdmin();
    }

    // --- Orders Collection ---
    match /orders/{orderId} {
      allow read: if request.auth != null && (
        resource.data.customerId == request.auth.uid ||
        (request.auth.uid in resource.data.vendorIds && isVendor()) ||
        isAdmin()
      );
      allow create: if request.auth != null &&
                       request.resource.data.customerId == request.auth.uid &&
                       isCustomer();
      allow update: if request.auth != null && (
        (resource.data.customerId == request.auth.uid && isCustomer() && (
          (request.resource.data.orderStatus == 'delivered' && 
           resource.data.orderStatus in ['pendingConfirmation', 'shipped']) ||
          (request.resource.data.orderStatus == 'cancelledByCustomer' && 
           resource.data.orderStatus in ['pendingConfirmation', 'shipped'])
        )) ||
        (request.auth.uid in resource.data.vendorIds && isVendor() && (
          (request.resource.data.orderStatus == 'shipped' && 
           resource.data.orderStatus == 'pendingConfirmation')
        )) ||
        isAdmin()
      );
      allow delete: if isAdmin();
    }

    // --- Disputes Collection ---
    match /disputes/{disputeId} {
      allow read: if request.auth != null && (
        resource.data.customerId == request.auth.uid ||
        (request.auth.uid in resource.data.vendorIds && isVendor()) ||
        isAdmin()
      );
      allow create: if request.auth != null &&
                       request.resource.data.customerId == request.auth.uid &&
                       isCustomer();
      allow update: if request.auth != null && (
        isAdmin() ||
        (request.auth.uid in resource.data.vendorIds && isVendor())
      );
      allow delete: if isAdmin();
    }

    // --- Vendor Ratings Collection ---
    match /vendor_ratings/{ratingId} {
      allow read: if true;
      allow create: if request.auth != null &&
                       request.resource.data.customerId == request.auth.uid &&
                       isCustomer();
      allow update: if request.auth != null && (
        (resource.data.customerId == request.auth.uid && isCustomer()) ||
        isAdmin()
      );
      allow delete: if isAdmin();
    }

    // --- Admin Collection ---
    match /admin/{document=**} {
      allow read, write: if isAdmin();
    }

    // --- Analytics Collection ---
    match /analytics/{document=**} {
      allow read: if request.auth != null && (
        resource.data.vendorId == request.auth.uid ||
        isAdmin()
      );
      allow write: if isAdmin();
    }
  }
}
```

## 📊 Required Firestore Composite Indexes

### Critical Indexes (Create these first):

1. **orders collection**:
   - Fields: `vendorIds` (Array), `createdAt` (Descending)
   - Query scope: Collection

2. **orders collection**:
   - Fields: `customerId` (Ascending), `createdAt` (Descending)
   - Query scope: Collection

3. **products collection**:
   - Fields: `vendorId` (Ascending), `createdAt` (Descending)
   - Query scope: Collection

4. **vendor_ratings collection**:
   - Fields: `vendorId` (Ascending), `createdAt` (Descending)
   - Query scope: Collection

5. **disputes collection**:
   - Fields: `vendorIds` (Array), `createdAt` (Descending)
   - Query scope: Collection

6. **disputes collection**:
   - Fields: `vendorIds` (Array), `status` (Ascending)
   - Query scope: Collection

### Additional Indexes (Create as needed):

7. **products collection**:
   - Fields: `category` (Ascending), `createdAt` (Descending)
   - Query scope: Collection

8. **vendor_ratings collection**:
   - Fields: `customerId` (Ascending), `createdAt` (Descending)
   - Query scope: Collection

9. **disputes collection**:
   - Fields: `customerId` (Ascending), `createdAt` (Descending)
   - Query scope: Collection

## 🚀 Setup Steps:

### 1. Apply Security Rules:
- Go to Firebase Console → Firestore Database → Rules
- Replace existing rules with the rules above
- Click "Publish"

### 2. Create Indexes:
**Option A: Auto-create (Recommended)**
- Run the app and use all features
- Check Firebase Console logs for index creation links
- Click the provided links to create indexes automatically

**Option B: Manual creation**
- Go to Firebase Console → Firestore Database → Indexes → Composite
- Create each index listed above manually

### 3. Run Migration:
- Open the app as a customer
- Go to menu (⋮) → "Run Migration"
- This adds `vendorIds` to existing orders for better performance

### 4. Test Features:
- Login as vendor → Test all dashboard tabs
- Login as customer → Place orders, rate vendors
- Verify all features work without permission errors

## 🔧 Troubleshooting:

### Permission Denied Errors:
- Check that security rules are applied correctly
- Verify user roles are set properly in user documents
- Ensure `vendorIds` field exists in orders (run migration)

### Index Errors:
- Wait 2-3 minutes for indexes to build after creation
- Check Firebase Console → Firestore → Indexes for build status
- Create missing indexes as prompted by error messages

### Performance Issues:
- Ensure all required indexes are created
- Consider pagination for large datasets
- Monitor query performance in Firebase Console
